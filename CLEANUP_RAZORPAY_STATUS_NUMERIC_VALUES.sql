-- =============================================
-- Cleanup Numeric razorpay_status Values
-- Description: Converts numeric razorpay_status values to appropriate text values
-- and fixes booking status mappings
-- =============================================

USE [CabYaari]
GO

PRINT '=== RAZORPAY STATUS CLEANUP ==='
PRINT 'Converting numeric razorpay_status values to text values'
PRINT 'Mapping: 1 = Pending, 2 = Paid, 3 = Failed'
PRINT ''

-- First, let's see the current distribution of razorpay_status values
PRINT 'Current distribution of razorpay_status values:'
SELECT 
    razorpay_status,
    COUNT(*) as Count,
    CASE 
        WHEN razorpay_status = '1' THEN 'Numeric 1 (should be Pending)'
        WHEN razorpay_status = '2' THEN 'Numeric 2 (should be Paid)'
        WHEN razorpay_status = '3' THEN 'Numeric 3 (should be Failed)'
        WHEN razorpay_status = 'Paid' THEN 'Text - Paid (correct)'
        WHEN razorpay_status = 'Failed' THEN 'Text - Failed (correct)'
        WHEN razorpay_status = 'Pending' THEN 'Text - Pending (correct)'
        WHEN razorpay_status IS NULL THEN 'NULL (needs default)'
        ELSE 'Other: ' + ISNULL(razorpay_status, 'NULL')
    END as Status_Description
FROM [dbo].[RLT_BOOKING]
GROUP BY razorpay_status
ORDER BY 
    CASE 
        WHEN razorpay_status IN ('1', '2', '3') THEN 1  -- Numeric values first
        WHEN razorpay_status IS NULL THEN 2             -- NULL values second
        ELSE 3                                          -- Text values last
    END,
    razorpay_status

PRINT ''
PRINT 'Current booking status correlation:'
SELECT 
    razorpay_status,
    Booking_Status_Id,
    COUNT(*) as Count
FROM [dbo].[RLT_BOOKING]
GROUP BY razorpay_status, Booking_Status_Id
ORDER BY razorpay_status, Booking_Status_Id

PRINT ''
PRINT 'Starting cleanup of numeric razorpay_status values...'

BEGIN TRANSACTION

-- Convert numeric razorpay_status values to text
-- 1 = Pending (incomplete payment)
UPDATE [dbo].[RLT_BOOKING]
SET razorpay_status = 'Pending',
    Booking_Status_Id = 3  -- Payment Pending status
WHERE razorpay_status = '1'

PRINT 'Updated razorpay_status = 1 to "Pending"'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

-- 2 = Paid (successful payment)
UPDATE [dbo].[RLT_BOOKING]
SET razorpay_status = 'Paid',
    Booking_Status_Id = 2  -- Successful status
WHERE razorpay_status = '2'

PRINT 'Updated razorpay_status = 2 to "Paid"'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

-- 3 = Failed (failed payment)
UPDATE [dbo].[RLT_BOOKING]
SET razorpay_status = 'Failed',
    Booking_Status_Id = 1  -- Failed status
WHERE razorpay_status = '3'

PRINT 'Updated razorpay_status = 3 to "Failed"'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

-- Handle NULL razorpay_status values - set to Pending by default
UPDATE [dbo].[RLT_BOOKING]
SET razorpay_status = 'Pending',
    Booking_Status_Id = 3  -- Payment Pending status
WHERE razorpay_status IS NULL OR razorpay_status = ''

PRINT 'Updated NULL/empty razorpay_status to "Pending"'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

-- Fix any remaining booking status inconsistencies
-- Ensure Paid status has correct booking status
UPDATE [dbo].[RLT_BOOKING]
SET Booking_Status_Id = 2  -- Successful status
WHERE razorpay_status IN ('Paid', 'COMPLETED')
AND Booking_Status_Id != 2

PRINT 'Fixed booking status for Paid payments'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

-- Ensure Failed status has correct booking status
UPDATE [dbo].[RLT_BOOKING]
SET Booking_Status_Id = 1  -- Failed status
WHERE razorpay_status IN ('Failed', 'FAILED')
AND Booking_Status_Id != 1

PRINT 'Fixed booking status for Failed payments'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

-- Ensure Pending status has correct booking status
UPDATE [dbo].[RLT_BOOKING]
SET Booking_Status_Id = 3  -- Payment Pending status
WHERE razorpay_status IN ('Pending', 'PENDING', 'Created', 'CREATED')
AND Booking_Status_Id NOT IN (1, 2)  -- Don't change processed payments

PRINT 'Fixed booking status for Pending payments'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

COMMIT TRANSACTION

PRINT ''
PRINT 'Cleanup completed successfully!'
PRINT ''
PRINT 'Updated distribution of razorpay_status values:'
SELECT 
    razorpay_status,
    COUNT(*) as Count,
    bs.BOOKING_STATUS as Booking_Status_Description
FROM [dbo].[RLT_BOOKING] b
LEFT JOIN [dbo].[RLT_BOOKING_STATUS] bs ON b.Booking_Status_Id = bs.PKID
GROUP BY razorpay_status, bs.BOOKING_STATUS
ORDER BY razorpay_status

PRINT ''
PRINT 'Final booking status correlation:'
SELECT 
    razorpay_status,
    Booking_Status_Id,
    bs.BOOKING_STATUS as Status_Description,
    COUNT(*) as Count
FROM [dbo].[RLT_BOOKING] b
LEFT JOIN [dbo].[RLT_BOOKING_STATUS] bs ON b.Booking_Status_Id = bs.PKID
GROUP BY razorpay_status, Booking_Status_Id, bs.BOOKING_STATUS
ORDER BY razorpay_status, Booking_Status_Id

PRINT ''
PRINT '=== RAZORPAY STATUS CLEANUP COMPLETED ==='
PRINT 'All numeric razorpay_status values have been converted to text:'
PRINT '- Numeric 1 → "Pending" (Booking_Status_Id = 3)'
PRINT '- Numeric 2 → "Paid" (Booking_Status_Id = 2)'
PRINT '- Numeric 3 → "Failed" (Booking_Status_Id = 1)'
PRINT '- NULL/Empty → "Pending" (Booking_Status_Id = 3)'
PRINT ''
PRINT 'Future payments will use the updated stored procedures that handle both numeric and text values.'
