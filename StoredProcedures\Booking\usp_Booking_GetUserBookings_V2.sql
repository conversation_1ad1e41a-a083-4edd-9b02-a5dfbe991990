-- =============================================
-- Stored Procedure: usp_Booking_GetUserBookings_V2
-- Description: Retrieves paginated user bookings with cursor-based pagination (Fixed Version)
-- Author: CabYaari Development Team
-- Created: 2025-07-26
-- Parameters:
--   @UserId: Username of the user (from JWT token)
--   @Cursor: Optional cursor for pagination (ISO date string or BookingId)
--   @PageSize: Number of records to return (max 10)
-- Returns: Paginated list of user bookings with metadata
-- =============================================

USE [CabYaari]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- Drop existing procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'usp_Booking_GetUserBookings_V2')
BEGIN
    DROP PROCEDURE [dbo].[usp_Booking_GetUserBookings_V2]
END
GO

CREATE PROCEDURE [dbo].[usp_Booking_GetUserBookings_V2]
    @UserId NVARCHAR(50),
    @Cursor NVARCHAR(50) = NULL,
    @PageSize INT = 10
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    -- Validate page size (max 10)
    IF @PageSize > 10
        SET @PageSize = 10;
    
    IF @PageSize <= 0
        SET @PageSize = 10;

    -- Parse cursor - can be either a date or a BookingId
    DECLARE @CursorDate DATETIME = NULL;
    DECLARE @CursorBookingId NVARCHAR(50) = NULL;
    
    PRINT '[DEBUG] Input parameters - UserId: ' + ISNULL(@UserId, 'NULL') + ', Cursor: ' + ISNULL(@Cursor, 'NULL') + ', PageSize: ' + CAST(@PageSize AS NVARCHAR(10));
    
    IF @Cursor IS NOT NULL AND @Cursor != ''
    BEGIN
        -- Try to parse as date first
        BEGIN TRY
            SET @CursorDate = CAST(@Cursor AS DATETIME);
            PRINT '[DEBUG] Cursor parsed as date: ' + CONVERT(NVARCHAR(30), @CursorDate, 121);
        END TRY
        BEGIN CATCH
            -- If not a date, treat as BookingId
            SET @CursorBookingId = @Cursor;
            PRINT '[DEBUG] Cursor treated as BookingId: ' + @CursorBookingId;
        END CATCH
    END
    ELSE
    BEGIN
        PRINT '[DEBUG] No cursor provided - first page request';
    END

    -- Debug: Show cursor conditions
    PRINT '[DEBUG] Cursor conditions:';
    PRINT '  @CursorDate IS NOT NULL: ' + CASE WHEN @CursorDate IS NOT NULL THEN 'TRUE' ELSE 'FALSE' END;
    PRINT '  @CursorBookingId IS NOT NULL: ' + CASE WHEN @CursorBookingId IS NOT NULL THEN 'TRUE' ELSE 'FALSE' END;
    PRINT '  Both cursors are NULL (first page): ' + CASE WHEN @CursorDate IS NULL AND @CursorBookingId IS NULL THEN 'TRUE' ELSE 'FALSE' END;

    -- Debug: Check user matching approaches
    DECLARE @BookingsByCreatedBy INT = 0;
    DECLARE @BookingsByUserJoin INT = 0;

    SELECT @BookingsByCreatedBy = COUNT(*)
    FROM [dbo].[RLT_BOOKING] b
    WHERE b.Booking_Created_By = @UserId;

    SELECT @BookingsByUserJoin = COUNT(*)
    FROM [dbo].[RLT_BOOKING] b
    INNER JOIN [Identity].[User] u ON CAST(b.Created_By AS NVARCHAR) = u.Id
    WHERE u.UserName = @UserId;

    PRINT '[DEBUG] User matching results:';
    PRINT '  Bookings found by Booking_Created_By: ' + CAST(@BookingsByCreatedBy AS NVARCHAR(10));
    PRINT '  Bookings found by Created_By + User join: ' + CAST(@BookingsByUserJoin AS NVARCHAR(10));

    -- Get bookings with pagination
    WITH BookingData AS (
        SELECT
            b.Booking_Id as BookingId,
            cf.CitY_Name as PickUpCity,
            ct.CitY_Name as DropOffCity,
            tt.Trip_Type as TripType,
            cc.Car_Category_Abbr as CarCategory,
            ROUND(b.Fare, 0) as Fare,  -- Round fare to no decimal places
            b.PickUp_Address as PickUpAddress,
            b.DropOff_Address as DropOffAddress,
            b.PickUp_Date as PickUpDate,
            b.PickUp_Time as PickUpTime,
            b.[Name] as TravelerName,
            b.Mobile_No1 as PhoneNumber,
            b.razorpay_status as RazorpayStatus,
            b.Created_Date as BookingDate,
            ROW_NUMBER() OVER (ORDER BY b.Created_Date DESC, b.Booking_Id DESC) as RowNum
        FROM [dbo].[RLT_BOOKING] b
        INNER JOIN [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
        INNER JOIN [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
        INNER JOIN [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
        INNER JOIN [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
        WHERE (
            -- Try to match using Booking_Created_By field (preferred for new bookings)
            b.Booking_Created_By = @UserId
            -- Fallback: Try to match using Created_By with User table join (for old bookings)
            OR EXISTS (
                SELECT 1 FROM [Identity].[User] u
                WHERE CAST(b.Created_By AS NVARCHAR) = u.Id
                AND u.UserName = @UserId
            )
        )
        AND (
            -- Date-based cursor (preferred when BookingDate is available)
            (@CursorDate IS NOT NULL AND b.Created_Date < @CursorDate)
            -- BookingId-based cursor (fallback when BookingDate is null)
            OR (@CursorBookingId IS NOT NULL AND b.Booking_Id < @CursorBookingId)
            -- No cursor (first page)
            OR (@CursorDate IS NULL AND @CursorBookingId IS NULL)
        )
    )
    SELECT TOP (@PageSize + 1) *
    FROM BookingData
    ORDER BY 
        CASE WHEN BookingDate IS NOT NULL THEN BookingDate ELSE '1900-01-01' END DESC,
        BookingId DESC;

END
GO

PRINT 'usp_Booking_GetUserBookings_V2 created successfully!'

-- =============================================
-- Usage Examples:
--
-- Get first page:
-- EXEC [dbo].[usp_Booking_GetUserBookings_V2] @UserId = 'testuser', @PageSize = 10
--
-- Get next page with date cursor (when BookingDate is available):
-- EXEC [dbo].[usp_Booking_GetUserBookings_V2] @UserId = 'testuser', @Cursor = '2025-07-06T10:30:00.000Z', @PageSize = 10
--
-- Get next page with BookingId cursor (when BookingDate is null):
-- EXEC [dbo].[usp_Booking_GetUserBookings_V2] @UserId = 'testuser', @Cursor = 'CY-190725-40282', @PageSize = 10
-- =============================================
