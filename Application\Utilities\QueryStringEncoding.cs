using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Application.Utilities
{
    public class QueryStringEncoding
    {
        public static string EncryptString(string Message, string Passphrase)
        {
            byte[] Results;
            UTF8Encoding UTF8 = new UTF8Encoding();

            MD5CryptoServiceProvider HashProvider = new MD5CryptoServiceProvider();
            byte[] TDESKey = HashProvider.ComputeHash(UTF8.GetBytes(Passphrase));

            TripleDESCryptoServiceProvider TDESAlgorithm = new TripleDESCryptoServiceProvider();
            TDESAlgorithm.Key = TDESKey;
            TDESAlgorithm.Mode = CipherMode.ECB;
            TDESAlgorithm.Padding = PaddingMode.PKCS7;

            byte[] DataToEncrypt = UTF8.GetBytes(Message);

            try
            {
                ICryptoTransform Encryptor = TDESAlgorithm.CreateEncryptor();
                Results = Encryptor.TransformFinalBlock(DataToEncrypt, 0, DataToEncrypt.Length);
            }
            finally
            {
                TDESAlgorithm.Clear();
                HashProvider.Clear();
            }

            return Convert.ToBase64String(Results);
        }

        public static string DecryptString(string Message, string Passphrase)
        {
            Message = Message.Replace(" ", "+");
            byte[] Results;
            UTF8Encoding UTF8 = new UTF8Encoding();

            MD5CryptoServiceProvider HashProvider = new MD5CryptoServiceProvider();
            byte[] TDESKey = HashProvider.ComputeHash(UTF8.GetBytes(Passphrase));

            TripleDESCryptoServiceProvider TDESAlgorithm = new TripleDESCryptoServiceProvider();
            TDESAlgorithm.Key = TDESKey;
            TDESAlgorithm.Mode = CipherMode.ECB;
            TDESAlgorithm.Padding = PaddingMode.PKCS7;

            byte[] DataToDecrypt = Convert.FromBase64String(Message);

            try
            {
                ICryptoTransform Decryptor = TDESAlgorithm.CreateDecryptor();
                Results = Decryptor.TransformFinalBlock(DataToDecrypt, 0, DataToDecrypt.Length);
            }
            finally
            {
                TDESAlgorithm.Clear();
                HashProvider.Clear();
            }

            return UTF8.GetString(Results);
        }

        // URL-Safe encryption optimized for payment links
        // Uses AES-256 with URL-safe Base64 encoding (no padding issues)
        public static string EncryptForUrl(string plainText, string passphrase)
        {
            try
            {
                byte[] plainTextBytes = Encoding.UTF8.GetBytes(plainText);

                using (Aes aes = Aes.Create())
                {
                    // Generate key from passphrase using PBKDF2 (more secure than MD5)
                    using (var keyDerivation = new Rfc2898DeriveBytes(passphrase, Encoding.UTF8.GetBytes("CabYaariSalt2024"), 10000))
                    {
                        aes.Key = keyDerivation.GetBytes(32); // 256-bit key
                        aes.IV = keyDerivation.GetBytes(16);  // 128-bit IV
                    }

                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    using (var encryptor = aes.CreateEncryptor())
                    using (var msEncrypt = new MemoryStream())
                    using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        csEncrypt.Write(plainTextBytes, 0, plainTextBytes.Length);
                        csEncrypt.FlushFinalBlock();

                        byte[] encryptedBytes = msEncrypt.ToArray();

                        // Convert to URL-safe Base64 (replace +/= with -_~ for URL compatibility)
                        string base64 = Convert.ToBase64String(encryptedBytes);
                        return base64.Replace('+', '-').Replace('/', '_').Replace('=', '~');
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Encryption failed", ex);
            }
        }

        // URL-Safe decryption for payment links
        public static string DecryptFromUrl(string encryptedText, string passphrase)
        {
            try
            {
                // Convert back from URL-safe Base64
                string base64 = encryptedText.Replace('-', '+').Replace('_', '/').Replace('~', '=');

                // Add padding if needed
                int padding = 4 - (base64.Length % 4);
                if (padding != 4)
                {
                    base64 += new string('=', padding);
                }

                byte[] encryptedBytes = Convert.FromBase64String(base64);

                using (Aes aes = Aes.Create())
                {
                    // Generate same key and IV from passphrase
                    using (var keyDerivation = new Rfc2898DeriveBytes(passphrase, Encoding.UTF8.GetBytes("CabYaariSalt2024"), 10000))
                    {
                        aes.Key = keyDerivation.GetBytes(32); // 256-bit key
                        aes.IV = keyDerivation.GetBytes(16);  // 128-bit IV
                    }

                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;

                    using (var decryptor = aes.CreateDecryptor())
                    using (var msDecrypt = new MemoryStream(encryptedBytes))
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    using (var srDecrypt = new StreamReader(csDecrypt))
                    {
                        return srDecrypt.ReadToEnd();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Decryption failed", ex);
            }
        }
    }
}
