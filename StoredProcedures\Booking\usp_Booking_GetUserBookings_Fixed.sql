-- =============================================
-- Stored Procedure: usp_Booking_GetUserBookings (Fixed Version)
-- Description: Fixed version with better JOIN handling
-- =============================================

USE [CabYaari]
GO

-- Drop existing procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'usp_Booking_GetUserBookings')
BEGIN
    DROP PROCEDURE [dbo].[usp_Booking_GetUserBookings]
END
GO

CREATE PROCEDURE [dbo].[usp_Booking_GetUserBookings]
    @UserId NVARCHAR(50),
    @Cursor NVARCHAR(50) = NULL,
    @PageSize INT = 10
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    -- Validate page size (max 10)
    IF @PageSize > 10
        SET @PageSize = 10;
    
    IF @PageSize <= 0
        SET @PageSize = 10;

    -- Parse cursor date
    DECLARE @CursorDate DATETIME = NULL;
    IF @Cursor IS NOT NULL AND @Cursor != ''
    BEGIN
        BEGIN TRY
            SET @CursorDate = CAST(@Cursor AS DATETIME);
        END TRY
        BEGIN CATCH
            SET @CursorDate = NULL;
        END CATCH
    END;

    -- First, let's find the user ID from the username
    DECLARE @UserGuid NVARCHAR(450);
    SELECT @UserGuid = Id FROM [Identity].[User] WHERE UserName = @UserId;

    -- If user not found, return empty result
    IF @UserGuid IS NULL
    BEGIN
        SELECT 
            '' as BookingId, '' as PickUpCity, '' as DropOffCity, '' as TripType, 
            '' as CarCategory, 0 as Fare, '' as PickUpAddress, '' as DropOffAddress,
            NULL as PickUpDate, '' as PickUpTime, '' as TravelerName, '' as PhoneNumber,
            '' as RazorpayStatus, NULL as BookingDate
        WHERE 1 = 0; -- Return empty result set with correct structure
        RETURN;
    END

    -- Get bookings with pagination using multiple JOIN approaches
    WITH BookingData AS (
        SELECT
            b.Booking_Id as BookingId,
            cf.CitY_Name as PickUpCity,
            ct.CitY_Name as DropOffCity,
            tt.Trip_Type as TripType,
            cc.Car_Category_Abbr as CarCategory,
            ROUND(b.Fare, 0) as Fare,
            b.PickUp_Address as PickUpAddress,
            b.DropOff_Address as DropOffAddress,
            b.PickUp_Date as PickUpDate,
            b.PickUp_Time as PickUpTime,
            b.[Name] as TravelerName,
            b.Mobile_No1 as PhoneNumber,
            b.razorpay_status as RazorpayStatus,
            b.Created_Date as BookingDate,
            ROW_NUMBER() OVER (ORDER BY b.Created_Date DESC) as RowNum
        FROM [dbo].[RLT_BOOKING] b
        INNER JOIN [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
        INNER JOIN [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
        INNER JOIN [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
        INNER JOIN [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
        WHERE (
            -- Try multiple approaches to match the user
            CAST(b.Created_By AS NVARCHAR(450)) = @UserGuid
            OR b.Created_By = TRY_CAST(@UserGuid AS INT)
            OR EXISTS (
                SELECT 1 FROM [Identity].[User] u 
                WHERE u.UserName = @UserId 
                AND (CAST(b.Created_By AS NVARCHAR(450)) = u.Id OR b.Created_By = TRY_CAST(u.Id AS INT))
            )
        )
        AND (@CursorDate IS NULL OR b.Created_Date < @CursorDate)
    )
    SELECT TOP (@PageSize + 1) *
    FROM BookingData
    ORDER BY BookingDate DESC;
END
GO

PRINT 'usp_Booking_GetUserBookings (Fixed Version) created successfully!'
