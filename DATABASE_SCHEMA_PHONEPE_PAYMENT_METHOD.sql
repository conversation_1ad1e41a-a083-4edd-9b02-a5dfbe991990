-- =============================================
-- Database Schema Changes for PhonePe Payment Method Mapping
-- Description: Add PhonePePaymentMethod column to RLT_PAYMENT_METHOD table
-- Date: 2025-07-19
-- =============================================

USE [CabYaari]
GO

-- Step 1: Add PhonePePaymentMethod column to RLT_PAYMENT_METHOD table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RLT_PAYMENT_METHOD]') AND name = 'PhonePePaymentMethod')
BEGIN
    ALTER TABLE [dbo].[RLT_PAYMENT_METHOD]
    ADD PhonePePaymentMethod nvarchar(50) NULL
    
    PRINT 'Added PhonePePaymentMethod column to RLT_PAYMENT_METHOD table'
END
ELSE
BEGIN
    PRINT 'PhonePePaymentMethod column already exists in RLT_PAYMENT_METHOD table'
END
GO

-- Step 2: Update existing records with PhonePe payment method mappings
-- Based on PhonePe documentation and common payment method names

UPDATE [dbo].[RLT_PAYMENT_METHOD] 
SET PhonePePaymentMethod = 'CASH'
WHERE PKID = 1 AND Payment_Method_Name = 'Cash'

UPDATE [dbo].[RLT_PAYMENT_METHOD] 
SET PhonePePaymentMethod = 'CREDIT_CARD'
WHERE PKID = 2 AND Payment_Method_Name = 'Credit Card'

UPDATE [dbo].[RLT_PAYMENT_METHOD] 
SET PhonePePaymentMethod = 'DEBIT_CARD'
WHERE PKID = 3 AND Payment_Method_Name = 'Debit Card'

UPDATE [dbo].[RLT_PAYMENT_METHOD] 
SET PhonePePaymentMethod = 'CARD'  -- Generic card type as fallback
WHERE PKID = 4 AND Payment_Method_Name = 'Online Payment'

UPDATE [dbo].[RLT_PAYMENT_METHOD] 
SET PhonePePaymentMethod = 'UPI'
WHERE PKID = 5 AND Payment_Method_Name = 'UPI'

UPDATE [dbo].[RLT_PAYMENT_METHOD]
SET PhonePePaymentMethod = 'NET_BANKING'  -- Official PhonePe type (not NETBANKING)
WHERE PKID = 6 AND Payment_Method_Name = 'Net Banking'

UPDATE [dbo].[RLT_PAYMENT_METHOD]
SET PhonePePaymentMethod = 'WALLET'  -- Official PhonePe type
WHERE PKID = 7 AND Payment_Method_Name = 'Wallet'

-- Note: BANKTRANSFER is not in official PhonePe docs, using ACCOUNT as fallback
UPDATE [dbo].[RLT_PAYMENT_METHOD]
SET PhonePePaymentMethod = 'ACCOUNT'  -- UPI/Bank account payments
WHERE PKID = 8 AND Payment_Method_Name = 'Bank Transfer'

PRINT 'Updated PhonePePaymentMethod mappings for existing payment methods'
GO

-- Step 3: Display the updated payment method mappings
SELECT 
    PKID,
    Payment_Method_Name,
    PhonePePaymentMethod,
    Is_Active
FROM [dbo].[RLT_PAYMENT_METHOD]
ORDER BY PKID

PRINT 'Current payment method mappings:'
GO

-- Step 4: Drop and recreate the stored procedure with PhonePe payment method lookup
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_Booking_Update_V2]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[usp_Booking_Update_V2]
GO

CREATE PROCEDURE [dbo].[usp_Booking_Update_V2]
    @BookingId nvarchar(30),
    @RazorpayPaymentId nvarchar(30),
    @RazorpayOrderid nvarchar(30),
    @RazorpaySignature nvarchar(30),
    @RazorpayStatus nvarchar(30),
    -- New parameters for partial payment support
    @PaymentType nvarchar(10) NULL,
    @PartialPaymentAmount decimal(18,2) NULL,
    @RemainingAmountForDriver decimal(18,2) NULL,
    -- New parameter for PhonePe payment method string
    @PhonePePaymentMethod nvarchar(50) NULL
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    DECLARE @errorMessage nvarchar(max),
           @ScriptRanOn datetime = GETDATE(),
           @TransactionId nvarchar(30),
           @BookingStatusId int,
           @BookingPKID int,
           @CurrentPaymentType nvarchar(10),
           @CurrentPartialAmount decimal(18,2),
           @CurrentRemainingAmount decimal(18,2),
           @ModeOfPaymentId int

    -- Lookup Mode_Of_Payment_Id based on PhonePe payment method
    IF @PhonePePaymentMethod IS NOT NULL
    BEGIN
        SELECT TOP 1 @ModeOfPaymentId = PKID
        FROM [dbo].[RLT_PAYMENT_METHOD]
        WHERE PhonePePaymentMethod = @PhonePePaymentMethod
        AND Is_Active = 1
        
        -- If no exact match found, try to find a fallback
        IF @ModeOfPaymentId IS NULL
        BEGIN
            -- For CREDIT_CARD or DEBIT_CARD, fallback to generic CARD
            IF @PhonePePaymentMethod IN ('CREDIT_CARD', 'DEBIT_CARD')
            BEGIN
                SELECT TOP 1 @ModeOfPaymentId = PKID
                FROM [dbo].[RLT_PAYMENT_METHOD]
                WHERE PhonePePaymentMethod = 'CARD'
                AND Is_Active = 1
            END

            -- For ACCOUNT type (UPI payments), try to map to UPI if available
            IF @PhonePePaymentMethod = 'ACCOUNT'
            BEGIN
                SELECT TOP 1 @ModeOfPaymentId = PKID
                FROM [dbo].[RLT_PAYMENT_METHOD]
                WHERE PhonePePaymentMethod = 'UPI'
                AND Is_Active = 1
            END

            -- If still no match, default to Online Payment (PKID = 4)
            IF @ModeOfPaymentId IS NULL
            BEGIN
                SET @ModeOfPaymentId = 4
            END
        END
        
        PRINT 'PhonePe Payment Method: ' + @PhonePePaymentMethod + ', Mapped to Mode_Of_Payment_Id: ' + CAST(@ModeOfPaymentId AS nvarchar(10))
    END

    -- Get current payment information
    SELECT
        @CurrentPaymentType = PaymentType,
        @CurrentPartialAmount = PartialPaymentAmount,
        @CurrentRemainingAmount = RemainingAmountForDriver
    FROM RLT_BOOKING
    WHERE Booking_Id = @BookingId

    -- Normalize razorpay_status: convert numeric values to text
    -- Handle legacy numeric status values: 1 = Pending, 2 = Paid, 3 = Failed
    SET @RazorpayStatus = CASE
        WHEN @RazorpayStatus = '1' THEN 'Pending'
        WHEN @RazorpayStatus = '2' THEN 'Paid'
        WHEN @RazorpayStatus = '3' THEN 'Failed'
        ELSE @RazorpayStatus  -- Keep original text values
    END

    -- Get the appropriate booking status ID based on payment status
    -- Updated to match RLT_BOOKING_STATUS table: 1=Failed, 2=Successful, 3=Payment Pending
    SET @BookingStatusId = CASE
        WHEN @RazorpayStatus IN ('Paid', 'COMPLETED') THEN 2  -- Successful booking (PKID 2)
        WHEN @RazorpayStatus IN ('Failed') THEN 1  -- Failed booking (PKID 1)
        ELSE 3  -- Payment Pending (PKID 3)
    END

    BEGIN TRANSACTION
        -- Update the booking record with payment information
        UPDATE RLT_BOOKING
        SET
            razorpay_payment_id = @RazorpayPaymentId,
            razorpay_signature = @RazorpaySignature,
            razorpay_status = @RazorpayStatus,
            Booking_Status_Id = @BookingStatusId,
            -- Update payment fields only if new values are provided
            PaymentType = ISNULL(@PaymentType, @CurrentPaymentType),
            PartialPaymentAmount = ISNULL(@PartialPaymentAmount, @CurrentPartialAmount),
            RemainingAmountForDriver = ISNULL(@RemainingAmountForDriver, @CurrentRemainingAmount),
            -- Update Mode_Of_Payment_Id based on PhonePe payment method lookup
            Mode_Of_Payment_Id = ISNULL(@ModeOfPaymentId, Mode_Of_Payment_Id),
            Updated_Date = GETDATE()
        WHERE
            Booking_Id = @BookingId

        -- Get the updated booking information for the response
        SELECT
            @TransactionId = razorpay_payment_id,
            @BookingPKID = PKID,
            @PaymentType = ISNULL(@PaymentType, PaymentType),
            @PartialPaymentAmount = ISNULL(@PartialPaymentAmount, PartialPaymentAmount),
            @RemainingAmountForDriver = ISNULL(@RemainingAmountForDriver, RemainingAmountForDriver)
        FROM
            RLT_BOOKING
        WHERE
            Booking_Id = @BookingId

        SET @errorMessage = CONVERT(nvarchar(max), GETDATE(), 21) + ' running usp_Booking_Update_V2 => committing sql transaction'
        RAISERROR(@errorMessage, 0, 1) WITH NOWAIT
    COMMIT TRANSACTION

    -- Return the booking and transaction information including payment details
    SELECT
        @BookingId AS BookingId,
        @TransactionId AS TransactionId,
        @RazorpayStatus AS PaymentStatus,
        @PaymentType AS PaymentType,
        @PartialPaymentAmount AS PartialPaymentAmount,
        @RemainingAmountForDriver AS RemainingAmountForDriver,
        @BookingStatusId AS BookingStatusId,
        @ModeOfPaymentId AS ModeOfPaymentId
END
GO

PRINT 'Stored procedure usp_Booking_Update_V2 updated successfully with PhonePe payment method lookup!'

-- Test the updated procedure (optional - comment out if not needed)
/*
-- Example test call
EXEC [dbo].[usp_Booking_Update_V2] 
    @BookingId = 'CY-170725-95186',
    @RazorpayPaymentId = 'OM2507191244501182375267',
    @RazorpayOrderid = 'OMO2507191244501172375404',
    @RazorpaySignature = '',
    @RazorpayStatus = 'COMPLETED',
    @PaymentType = 'FULL',
    @PartialPaymentAmount = NULL,
    @RemainingAmountForDriver = 0.00,
    @PhonePePaymentMethod = 'CREDIT_CARD'  -- This will be looked up to get Mode_Of_Payment_Id = 2
*/
