// =============================================
// Secure Booking Get Implementation
// =============================================

// 1. Secure Response DTO
namespace Application.DTOs.Booking
{
    public class BookingDetailsResponse
    {
        public string BookingID { get; set; }
        public string PickUpCity { get; set; }
        public string DropOffCity { get; set; }
        public string TripType { get; set; }
        public string CarCategory { get; set; }
        public string Duration { get; set; }
        public decimal? Distance { get; set; }
        public decimal? BasicFare { get; set; }
        public decimal? DriverCharge { get; set; }
        public decimal? Gst { get; set; }
        public decimal? Fare { get; set; }
        public decimal? GstFare { get; set; }
        public string CouponCode { get; set; }
        public decimal? CouponDiscount { get; set; }
        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }
        public DateTime? PickUpDate { get; set; }
        public string PickUpTime { get; set; }
        public string TravelerName { get; set; }
        public int? PaymentMode { get; set; }
        public string PaymentStatus { get; set; }
        public decimal CashAmountToPayDriver { get; set; }
        public int PaymentOption { get; set; }
        public decimal? TollCharge { get; set; }
        public string PaymentType { get; set; }
        public decimal? PartialPaymentAmount { get; set; }
        public decimal? RemainingAmountForDriver { get; set; }
        
        // ❌ EXCLUDED sensitive fields:
        // - PhoneNumber
        // - MailId  
        // - RazorpayPaymentId
        // - RazorpaySignature
        // - BookingCreatedBy
        // - PickUpAddressLongLat (exact coordinates)
    }
}

// 2. Updated Controller with Security
namespace WebApi.Controllers.v1
{
    [ApiVersion("1.0")]
    public class BookingController : BaseApiController
    {
        private readonly IBookingRepositoryAsync bookingRepositoryAsync;

        public BookingController(IBookingRepositoryAsync bookingRepositoryAsync)
        {
            this.bookingRepositoryAsync = bookingRepositoryAsync;
        }
      
        // GET api/<controller>/5
        [HttpGet("{bookingId:regex(^BK[0-9]{{13}}$)}")]  // ✅ Validate booking ID format
        [Authorize]  // ✅ Require authentication
        public async Task<IActionResult> Get(string bookingId)
        {
            // ✅ Get authenticated user ID
            var userId = User.FindFirst("uid")?.Value;
            
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { Message = "User ID not found in token" });
            }

            // ✅ Validate input
            if (string.IsNullOrWhiteSpace(bookingId))
            {
                return BadRequest(new { Message = "Booking ID is required" });
            }

            try
            {
                var query = new GetBookingByIdQuery 
                { 
                    BookingId = bookingId,
                    RequestingUserId = userId  // ✅ Pass user ID for ownership check
                };
                
                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid(new { Message = "You don't have access to this booking" });
            }
            catch (ApiException ex)
            {
                return NotFound(new { Message = ex.Message });
            }
            catch (Exception ex)
            {
                // Log the error but don't expose internal details
                Console.WriteLine($"Error retrieving booking {bookingId} for user {userId}: {ex.Message}");
                return StatusCode(500, new { Message = "Error retrieving booking details" });
            }
        }

        // ... other methods remain the same
    }
}

// 3. Updated Query with Ownership Validation
namespace Application.Features.Bookings.GetBookingById
{
    public class GetBookingByIdQuery : IRequest<Response<BookingDetailsResponse>>
    {
        public string BookingId { get; set; }
        public string RequestingUserId { get; set; }  // ✅ Add user ID for ownership check
    }

    public class GetBookingByIdQueryHandler : IRequestHandler<GetBookingByIdQuery, Response<BookingDetailsResponse>>
    {
        private readonly IBookingRepositoryAsync _bookingRepositoryAsync;
        private readonly IMapper _mapper;

        public GetBookingByIdQueryHandler(IBookingRepositoryAsync bookingRepositoryAsync, IMapper mapper)
        {
            _bookingRepositoryAsync = bookingRepositoryAsync;
            _mapper = mapper;
        }

        public async Task<Response<BookingDetailsResponse>> Handle(GetBookingByIdQuery query, CancellationToken cancellationToken)
        {
            var booking = await _bookingRepositoryAsync.GetByUniqueIdAsync(query.BookingId);
            
            if (booking == null) 
                throw new ApiException("Booking Not Found.");
            
            // ✅ Ownership validation
            if (booking.BookingCreatedBy != query.RequestingUserId)
            {
                throw new UnauthorizedAccessException("You don't have access to this booking");
            }
            
            // ✅ Map to secure DTO (excludes sensitive data)
            var response = _mapper.Map<BookingDetailsResponse>(booking);
            
            // ✅ Map payment status from internal to user-friendly
            response.PaymentStatus = MapPaymentStatus(booking.RazorpayStatus);
            
            return new Response<BookingDetailsResponse>(response);
        }

        private string MapPaymentStatus(string internalStatus)
        {
            return internalStatus?.ToLower() switch
            {
                "paid" => "Completed",
                "pending" => "Pending",
                "failed" => "Failed",
                _ => "Unknown"
            };
        }
    }
}

// 4. AutoMapper Profile Update
namespace Application.Mappings
{
    public class GeneralProfile : Profile
    {
        public GeneralProfile()
        {
            // Existing mappings...
            CreateMap<BookingCommand, RLT_BOOKING>();
            
            // ✅ Add secure booking mapping
            CreateMap<RLT_BOOKING, BookingDetailsResponse>()
                .ForMember(dest => dest.PaymentStatus, opt => opt.Ignore()); // Handled in query handler
        }
    }
}

// 5. Custom Exception for Unauthorized Access
namespace Application.Exceptions
{
    public class UnauthorizedAccessException : Exception
    {
        public UnauthorizedAccessException(string message) : base(message)
        {
        }
    }
}

// 6. Input Validation Attribute (Optional Enhancement)
namespace WebApi.Attributes
{
    public class ValidBookingIdAttribute : ValidationAttribute
    {
        public override bool IsValid(object value)
        {
            if (value is string bookingId)
            {
                return System.Text.RegularExpressions.Regex.IsMatch(bookingId, @"^CY-[0-9]{6}-[0-9]{5}$");
            }
            return false;
        }

        public override string FormatErrorMessage(string name)
        {
            return $"The {name} field must be a valid booking ID format (CY-DDMMYY-NNNNN).";
        }
    }
}

// 7. Rate Limiting Configuration (in Startup.cs)
/*
public void ConfigureServices(IServiceCollection services)
{
    // Add rate limiting
    services.AddRateLimiter(options =>
    {
        options.AddFixedWindowLimiter("BookingPolicy", opt =>
        {
            opt.PermitLimit = 10;  // 10 requests
            opt.Window = TimeSpan.FromMinutes(1);  // per minute
            opt.QueueProcessingOrder = QueueProcessingOrder.OldestFirst;
            opt.QueueLimit = 2;
        });
    });
}

public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    // Use rate limiting
    app.UseRateLimiter();
}
*/
