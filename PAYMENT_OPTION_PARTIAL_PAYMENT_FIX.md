# Payment Option Partial Payment Fix

## Issue Identified

**Problem:** Partial payment requests were being saved as full payments in the database.

### Request Analysis:
```json
{
  "paymentOption": 1,        // PartialPay enum value
  "fare": 1000,              // Amount to pay online
  "cashAmountToPayDriver": 3200,  // Amount to pay driver
  "basicFare": 4000,         // Total trip cost
  "gst": 200                 // GST amount
}
```

### Database Result (Before Fix):
- `PaymentOption = 2` (FullPay - INCORRECT)
- `PaymentType = "FULL"` (INCORRECT)
- `Fare = 1000.00`
- `CashAmountToPayDriver = 3200`

### Expected Database Result (After Fix):
- `PaymentOption = 1` (PartialPay - CORRECT)
- `PaymentType = "PARTIAL"` (CORRECT)
- `PartialPaymentAmount = 1000.00`
- `RemainingAmountForDriver = 3400` (3200 + 200 GST)

## Root Cause

The issue was in the `GeneratePhonePeToken.cs` handler:

1. **Missing PaymentType Determination**: The request doesn't include a `PaymentType` field
2. **Logic Dependency**: The payment logic assumed `PaymentType` was already set
3. **Default Behavior**: When `PaymentType` was null/empty, it defaulted to "FULL"
4. **AutoMapper Mapping**: AutoMapper then set `PaymentOption = 2` based on `PaymentType = "FULL"`

## Solution Implemented

### 1. Added PaymentType Determination Logic
**File:** `Application/Features/Payments/PhonePe/GeneratePhonePeToken.cs`

```csharp
// Determine PaymentType based on PaymentOption if not already set
if (string.IsNullOrEmpty(request.BookingCommand.PaymentType))
{
    request.BookingCommand.PaymentType = request.BookingCommand.PaymentOption switch
    {
        (int)PaymentOption.PartialPay => "PARTIAL",
        (int)PaymentOption.FullPay => "FULL",
        _ => "FULL" // Default to FULL
    };
    Console.WriteLine($"Determined PaymentType from PaymentOption: {request.BookingCommand.PaymentOption} -> {request.BookingCommand.PaymentType}");
}

// For partial payments, set PartialPaymentAmount if not already set
if (request.BookingCommand.PaymentType == "PARTIAL" && !request.BookingCommand.PartialPaymentAmount.HasValue)
{
    request.BookingCommand.PartialPaymentAmount = request.BookingCommand.Fare.Value;
    Console.WriteLine($"Set PartialPaymentAmount from Fare: {request.BookingCommand.PartialPaymentAmount}");
}
```

### 2. Enhanced AutoMapper Logic
The existing AutoMapper configuration will now work correctly because `PaymentType` is properly set:

```csharp
private static int MapPaymentTypeToPaymentOption(string paymentType)
{
    return paymentType?.ToUpper() switch
    {
        "PARTIAL" => (int)PaymentOption.PartialPay,  // 1 = Partial payment
        "FULL" => (int)PaymentOption.FullPay,        // 2 = Full online payment
        _ => (int)PaymentOption.FullPay               // Default to full payment (2)
    };
}
```

### 3. Stored Procedure Backup Logic
The stored procedures also have backup logic to ensure consistency:

```sql
-- Set PaymentOption based on PaymentType if not provided or incorrect
IF @PaymentOption IS NULL OR @PaymentOption = 0
BEGIN
    SET @PaymentOption = CASE 
        WHEN @PaymentType = 'PARTIAL' THEN 1  -- PartialPay
        WHEN @PaymentType = 'FULL' THEN 2     -- FullPay
        ELSE 2  -- Default to FullPay
    END
END
```

## Flow for Partial Payment Request

### Input:
```json
{
  "paymentOption": 1,
  "fare": 1000,
  "cashAmountToPayDriver": 3200,
  "gst": 200
}
```

### Processing Steps:
1. **PaymentType Determination**: `PaymentOption = 1` → `PaymentType = "PARTIAL"`
2. **PartialPaymentAmount Setting**: `PartialPaymentAmount = 1000` (from fare)
3. **RemainingAmountForDriver Calculation**: `3200 + 200 = 3400`
4. **AutoMapper Validation**: `PaymentType = "PARTIAL"` → `PaymentOption = 1` ✅
5. **Database Storage**: Consistent values stored

### Expected Database Result:
- `PaymentOption = 1` (PartialPay)
- `PaymentType = "PARTIAL"`
- `PartialPaymentAmount = 1000.00`
- `RemainingAmountForDriver = 3400.00`
- `Fare = 1000.00` (amount to pay online)
- `CashAmountToPayDriver = 3200.00`

## Testing

To test this fix, use the same curl request:

```bash
curl "https://devapi.cabyaari.com/api/v1/payments/phonepe/token" \
  -H "Content-Type: application/json" \
  --data-raw '{
    "paymentOption": 1,
    "fare": 1000,
    "cashAmountToPayDriver": 3200,
    "gst": 200,
    "basicFare": 4000,
    ...
  }'
```

**Expected Result:** Database should now show `PaymentOption = 1` and `PaymentType = "PARTIAL"`.

## Summary

The fix ensures that:
1. **PaymentOption = 1** correctly maps to **PaymentType = "PARTIAL"**
2. **PaymentOption = 2** correctly maps to **PaymentType = "FULL"**
3. **Partial payment amounts** are properly calculated and stored
4. **Database consistency** is maintained across all payment flows

This resolves the issue where partial payment requests were incorrectly being stored as full payments.
