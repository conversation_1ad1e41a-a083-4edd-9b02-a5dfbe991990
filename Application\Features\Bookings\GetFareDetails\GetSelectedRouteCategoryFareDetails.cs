﻿using Application.DTOs.BasicCommon;
using Application.DTOs.Booking;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using Dapper;
using Domain.Settings;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.Bookings.GetFareDetails
{
    public class GetSelectedRouteCategoryFareDetails : IRequest<Response<SelectedRoutesCategoryResponse>>
    {
        public string PickUpAddressLongLat { get; set; }

        public string DropOffAddressLongLat { get; set; }

        public string TripType { get; set; }

        public string CarCagetgory { get; set; }



        public class GetSelectedRouteCategoryFareDetailsHandler : IRequestHandler<GetSelectedRouteCategoryFareDetails, Response<SelectedRoutesCategoryResponse>>
        {
            private readonly IBookingRepositoryAsync _bookingRepositoryAsync;
            private readonly IConfiguration _configuration;
            private readonly IHttpClientFactory _httpClientFactory;
            public MapSettings _mapSettings { get; }
            public FareSettings _fareSettings { get; }
            public GetSelectedRouteCategoryFareDetailsHandler(IBookingRepositoryAsync bookingRepositoryAsync, IConfiguration configuration, IOptions<MapSettings> mapSettings, IOptions<FareSettings> fareSettings, IHttpClientFactory httpClientFactory)
            {
                _bookingRepositoryAsync = bookingRepositoryAsync;
                _configuration = configuration;
                _mapSettings = mapSettings.Value;
                _fareSettings = fareSettings.Value;
                _httpClientFactory = httpClientFactory;
            }

            /// <summary>
            /// Calculates billing distance by adding detour buffer to actual distance
            /// Time compensation logic has been commented out as per business requirement
            /// </summary>
            /// <param name="actualDistanceKM">Actual distance from API in kilometers</param>
            /// <param name="journeyTimeSeconds">Journey time from API in seconds</param>
            /// <returns>Distance to use for billing (actual + detour buffer)</returns>
            private int CalculateCompensatedDistance(int actualDistanceKM, int journeyTimeSeconds)
            {
                Console.WriteLine($"Distance Calculation Details:");
                Console.WriteLine($"  Actual Distance: {actualDistanceKM} KM");
                Console.WriteLine($"  Journey Time: {journeyTimeSeconds} seconds ({journeyTimeSeconds / 3600.0:F2} hours)");

                // Use actual distance as base (time compensation logic commented out)
                int baseDistance = actualDistanceKM;

                /* TIME COMPENSATION LOGIC COMMENTED OUT AS PER BUSINESS REQUIREMENT
                // Add configurable time buffer to journey time as per business requirement
                int bufferedTimeSeconds = journeyTimeSeconds + (_fareSettings.TimeBufferMinutes * 60);
                double bufferedTimeHours = bufferedTimeSeconds / 3600.0;

                // Calculate average speed: Distance / Time
                double averageSpeed = actualDistanceKM / bufferedTimeHours;

                Console.WriteLine($"Journey Analysis:");
                Console.WriteLine($"  Actual Time: {journeyTimeSeconds / 3600.0:F2} hours");
                Console.WriteLine($"  Buffered Time: {bufferedTimeHours:F2} hours (+{_fareSettings.TimeBufferMinutes} min buffer)");
                Console.WriteLine($"  Average Speed: {averageSpeed:F1} km/h");

                // Time-based compensation logic using configurable thresholds
                if (averageSpeed < _fareSettings.TimeCompensationSpeedThresholdKmh)
                {
                    // Compensated distance = standard speed × buffered journey time
                    int compensatedDistance = (int)Math.Round(_fareSettings.TimeCompensationStandardSpeedKmh * bufferedTimeHours);

                    // Use the higher value between actual and compensated distance
                    baseDistance = Math.Max(actualDistanceKM, compensatedDistance);

                    Console.WriteLine($"  Speed < {_fareSettings.TimeCompensationSpeedThresholdKmh} km/h: Applying time compensation");
                    Console.WriteLine($"  Compensated Distance: {compensatedDistance} KM ({_fareSettings.TimeCompensationStandardSpeedKmh} km/h × {bufferedTimeHours:F2} hrs)");
                    Console.WriteLine($"  Base Distance: {baseDistance} KM (max of actual vs compensated)");
                }
                else
                {
                    Console.WriteLine($"  Speed >= {_fareSettings.TimeCompensationSpeedThresholdKmh} km/h: No time compensation needed");
                }
                */

                // Add detour buffer to account for route variations
                int finalDistance = baseDistance + _fareSettings.DetourBufferKM;
                Console.WriteLine($"  Adding Detour Buffer: +{_fareSettings.DetourBufferKM} KM");
                Console.WriteLine($"  Final Billing Distance: {finalDistance} KM (Base: {baseDistance} + Buffer: {_fareSettings.DetourBufferKM})");

                return finalDistance;
            }

            /// <summary>
            /// Calls Google Maps Distance Matrix API to get distance and duration
            /// </summary>
            /// <param name="fromLatLong">Origin coordinates in "lat,lng" format</param>
            /// <param name="toLatLong">Destination coordinates in "lat,lng" format</param>
            /// <returns>Tuple containing distance in meters and duration in seconds</returns>
            private async Task<(int distanceMeters, int durationSeconds)> GetGoogleMapsDistanceAndDuration(string fromLatLong, string toLatLong)
            {
                var stopwatch = Stopwatch.StartNew();
                Console.WriteLine($"[PERF] Starting Google Maps API call at {DateTime.Now:HH:mm:ss.fff}");

                try
                {
                    var apiKey = _configuration["GoogleMaps:ApiKey"];
                    var url = $"https://maps.googleapis.com/maps/api/distancematrix/json?origins={fromLatLong}&destinations={toLatLong}&units=metric&key={apiKey}";

                    Console.WriteLine($"Google Maps Distance Matrix API URL: {url}");

                    var httpStopwatch = Stopwatch.StartNew();
                    using var httpClient = _httpClientFactory.CreateClient();

                    // Set timeout for HTTP client to identify if network is the bottleneck
                    httpClient.Timeout = TimeSpan.FromSeconds(30);

                    Console.WriteLine($"[PERF] Making HTTP request to Google Maps API...");
                    var response = await httpClient.GetStringAsync(url);
                    httpStopwatch.Stop();

                    Console.WriteLine($"[PERF] HTTP request completed in {httpStopwatch.ElapsedMilliseconds} ms");
                    Console.WriteLine($"[PERF] Response size: {response.Length} characters");
                    Console.WriteLine($"Google Maps API Response: {response}");

                    var parseStopwatch = Stopwatch.StartNew();
                    var googleResponse = JsonConvert.DeserializeObject<GoogleDistanceMatrixResponse>(response);
                    parseStopwatch.Stop();

                    Console.WriteLine($"[PERF] JSON parsing completed in {parseStopwatch.ElapsedMilliseconds} ms");

                    if (googleResponse?.rows?.Length > 0 &&
                        googleResponse.rows[0]?.elements?.Length > 0 &&
                        googleResponse.rows[0].elements[0].status == "OK")
                    {
                        var element = googleResponse.rows[0].elements[0];
                        int distanceMeters = element.distance.value;
                        int durationSeconds = element.duration.value;

                        stopwatch.Stop();
                        Console.WriteLine($"[PERF] Google Maps API call completed successfully in {stopwatch.ElapsedMilliseconds} ms");
                        Console.WriteLine($"[PERF] Breakdown - HTTP: {httpStopwatch.ElapsedMilliseconds} ms, JSON Parse: {parseStopwatch.ElapsedMilliseconds} ms");

                        Console.WriteLine($"Google Maps Results:");
                        Console.WriteLine($"  Distance: {distanceMeters} meters ({distanceMeters / 1000.0:F2} km)");
                        Console.WriteLine($"  Duration: {durationSeconds} seconds ({durationSeconds / 3600.0:F2} hours)");

                        return (distanceMeters, durationSeconds);
                    }
                    else
                    {
                        stopwatch.Stop();
                        Console.WriteLine($"[PERF] Google Maps API call failed after {stopwatch.ElapsedMilliseconds} ms - no valid results");
                        Console.WriteLine("Google Maps API returned no valid results");
                        throw new Exception("Google Maps API returned no valid results");
                    }
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    Console.WriteLine($"[PERF] Google Maps API call failed after {stopwatch.ElapsedMilliseconds} ms - Exception: {ex.Message}");
                    Console.WriteLine($"Error calling Google Maps API: {ex.Message}");
                    throw new Exception($"Failed to get distance and duration from Google Maps: {ex.Message}");
                }
            }

            public async Task<Response<SelectedRoutesCategoryResponse>> Handle(GetSelectedRouteCategoryFareDetails query, CancellationToken cancellationToken)
            {
                string perKmCharges = string.Empty;
                string bookingRuleFare = string.Empty;
                string carCategoryId = string.Empty;
                int tripTypeId = 0;

                CarCategory carCategory = new CarCategory();

                BookingFareRules bookingFareRules = new BookingFareRules();
                SelectedRoutesCategoryResponse responsesSelectedRoute = new SelectedRoutesCategoryResponse();

                int Per_KM_fare = 0;
                decimal Basic_Fare = 0, GST_Fare;
                string fixRateNote = "";

                Console.WriteLine("=== GetSelectedRouteCategoryFareDetails Handler Started ===");
                Console.WriteLine($"PickUp: {query.PickUpAddressLongLat}");
                Console.WriteLine($"DropOff: {query.DropOffAddressLongLat}");
                Console.WriteLine($"TripType: {query.TripType}");
                Console.WriteLine($"Category: {query.CarCagetgory}");

                // Call Google Maps Distance Matrix API
                Console.WriteLine($"[PERF] Starting Google Maps API call...");
                var googleMapsStopwatch = Stopwatch.StartNew();
                var (distanceMeters, durationSeconds) = await GetGoogleMapsDistanceAndDuration(query.PickUpAddressLongLat, query.DropOffAddressLongLat);
                googleMapsStopwatch.Stop();
                Console.WriteLine($"[PERF] Google Maps API call completed in {googleMapsStopwatch.ElapsedMilliseconds} ms");

                int actualDistanceKM = distanceMeters / 1000; // Convert meters to kilometers
                int journeyTimeSeconds = durationSeconds;

                int hours = journeyTimeSeconds / 3600;
                int mins = (journeyTimeSeconds % 3600) / 60;
                string duration = hours + " hrs " + mins + " mins ";

                Console.WriteLine($"Actual Distance: {actualDistanceKM} KM");
                Console.WriteLine($"Journey Time: {duration}");

                // Apply time-based fare compensation logic
                int distances = CalculateCompensatedDistance(actualDistanceKM, journeyTimeSeconds);

                Console.WriteLine($"Billing Distance (after compensation): {distances} KM");

                var tripTypeSQL = "SELECT TOP 1 PKID FROM RLT_TRIP_TYPES WHERE Trip_Type ='" + query.TripType + "'";
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                    var tripType = await connection.QuerySingleOrDefaultAsync<int>(tripTypeSQL);
                    tripTypeId = tripType;
                    connection.Close();
                }

                var perKmChargesSQL = "SELECT PKID,Car_Category_Abbr as CategoryName,Features,Capacity,Car_Categroy_Image as CategoryImage ,Per_KM_fare as PerKMCharges, Base_Fare as BaseFare FROM RLT_CAR_CATEGORY WHERE Is_Active =1 and Car_Category_Abbr='" + query.CarCagetgory + "'";
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                    var response = await connection.QuerySingleOrDefaultAsync<CarCategory>(perKmChargesSQL);


                    carCategory.PKID = response.PKID;
                    carCategory.CategoryName = response.CategoryName;
                    carCategory.CategoryImage = response.CategoryImage;

                    carCategory.PerKMCharges = response.PerKMCharges;
                    carCategory.BaseFare = response.BaseFare;
                    carCategory.Capacity = response.Capacity;
                    carCategory.Features = response.Features;


                    connection.Close();
                }

                var bookingRuleSQL = "SELECT PKID,Car_Category_Id as CarCategoryId,Trip_Type_Id as TripType, NewDistance as FixedFare FROM RLT_BOOKING_RULES WHERE Trip_Type_Id=" + tripTypeId + " and Car_Category_Id =" + carCategory.PKID + " and Distance_From <= " + distances + " and Distance_To >= " + distances + "";
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    connection.Open();
                    var RulesFare = await connection.QuerySingleOrDefaultAsync(bookingRuleSQL);
                    if (RulesFare != null)
                    {
                        bookingFareRules.PKID = RulesFare.PKID;
                        bookingFareRules.FixedFare = RulesFare.FixedFare;
                        bookingFareRules.TripType = RulesFare.TripType;
                        bookingFareRules.CarCategoryId = RulesFare.TripType;
                    }
                    connection.Close();

                }


                if (bookingFareRules.FixedFare >0)
                {

                    decimal fixedFare = bookingFareRules.FixedFare;
                    decimal gstAmount = (fixedFare * 5) / 100;
                    decimal finalFare = fixedFare + gstAmount;

                    responsesSelectedRoute.Distance = actualDistanceKM.ToString("#.##");  // Show actual distance to user
                    responsesSelectedRoute.ActualDistance = actualDistanceKM.ToString("#.##");  // Keep for compatibility
                    responsesSelectedRoute.BasicFare = Math.Round(fixedFare, 0).ToString();
                    responsesSelectedRoute.GSTFare = Math.Round(gstAmount, 0).ToString();
                    responsesSelectedRoute.Fare = Math.Round(finalFare, 0).ToString();
                    responsesSelectedRoute.Duration = duration;
                    responsesSelectedRoute.FixRateNote = "1 KM to 125 KM we charge fix "+ responsesSelectedRoute.Fare + " rs";
                    responsesSelectedRoute.Capacity = carCategory.Capacity;
                    responsesSelectedRoute.CategoryName = carCategory.CategoryName;
                    responsesSelectedRoute.CategoryImage = carCategory.CategoryImage;
                    responsesSelectedRoute.PerKMCharges = carCategory.PerKMCharges;
                    responsesSelectedRoute.Capacity = carCategory.Capacity;
                    responsesSelectedRoute.Features = carCategory.Features;

                }
                else
                {
                    // NEW PRICING LOGIC: MAX(Base Fare, Distance Fare) + GST
                    decimal baseFare = carCategory.BaseFare;
                    decimal originalPerKmRate = Convert.ToDecimal(carCategory.PerKMCharges);

                    // Apply configurable distance-based rate adjustment for short trips
                    decimal adjustedPerKmRate = originalPerKmRate;
                    if (distances < _fareSettings.ShortDistanceThresholdKM)
                    {
                        adjustedPerKmRate = originalPerKmRate + _fareSettings.ShortDistanceRateAdjustment;
                        Console.WriteLine($"Distance < {_fareSettings.ShortDistanceThresholdKM} KM: Adjusted per KM rate from ₹{originalPerKmRate} to ₹{adjustedPerKmRate} (+₹{_fareSettings.ShortDistanceRateAdjustment})");
                    }
                    else
                    {
                        Console.WriteLine($"Distance >= {_fareSettings.ShortDistanceThresholdKM} KM: Using original per KM rate ₹{originalPerKmRate}");
                    }

                    // Calculate distance fare
                    decimal distanceFare = adjustedPerKmRate * distances;

                    // NEW LOGIC: Take maximum of base fare or distance fare
                    decimal basicFare = Math.Max(baseFare, distanceFare);
                    decimal gstAmount = (basicFare * 5) / 100;
                    decimal finalFare = basicFare + gstAmount;

                    // Build comprehensive rate note with new pricing logic
                    string fareType = basicFare == baseFare ? "Base Fare" : "Distance Fare";
                    string rateNote = $"{fareType}: ₹{basicFare.ToString("#.##")} [MAX(Base: ₹{baseFare}, Distance: ₹{distanceFare.ToString("#.##")})]";

                    // Add distance calculation details
                    rateNote += $" (Distance: {distances} KM × ₹{adjustedPerKmRate}/KM)";

                    // Add distance-based rate adjustment note
                    if (distances < _fareSettings.ShortDistanceThresholdKM)
                    {
                        rateNote += $" [+₹{_fareSettings.ShortDistanceRateAdjustment}/KM for trips < {_fareSettings.ShortDistanceThresholdKM} KM]";
                    }

                    // Add detour buffer note
                    rateNote += $" [Detour buffer: +{_fareSettings.DetourBufferKM} KM]";

                    /* TIME COMPENSATION NOTE REMOVED - NO LONGER APPLICABLE
                    // Add time compensation note if billing distance differs from actual distance + buffer
                    int actualPlusBuffer = actualDistanceKM + _fareSettings.DetourBufferKM;
                    if (distances != actualPlusBuffer)
                    {
                        rateNote += $" [Time compensation: billing {distances} KM vs actual+buffer {actualPlusBuffer} KM]";
                    }
                    */

                    responsesSelectedRoute.Distance = actualDistanceKM.ToString("#.##");  // Show actual distance to user
                    responsesSelectedRoute.ActualDistance = actualDistanceKM.ToString("#.##");  // Keep for compatibility
                    responsesSelectedRoute.BasicFare = Math.Round(basicFare, 0).ToString();
                    responsesSelectedRoute.GSTFare = Math.Round(gstAmount, 0).ToString();
                    responsesSelectedRoute.Fare = Math.Round(finalFare, 0).ToString();
                    responsesSelectedRoute.Duration = duration;
                    responsesSelectedRoute.FixRateNote = rateNote;
                    responsesSelectedRoute.Capacity = carCategory.Capacity;
                    responsesSelectedRoute.CategoryName = carCategory.CategoryName;
                    responsesSelectedRoute.CategoryImage = carCategory.CategoryImage;
                    responsesSelectedRoute.PerKMCharges = carCategory.PerKMCharges;
                    responsesSelectedRoute.Features = carCategory.Features;

                }
            

                return new Response<SelectedRoutesCategoryResponse>(responsesSelectedRoute);

            }
    }

    // Google Maps Distance Matrix API Response Models
    public class GoogleDistanceMatrixResponse
    {
        public string status { get; set; }
        public GoogleDistanceMatrixRow[] rows { get; set; }
    }

    public class GoogleDistanceMatrixRow
    {
        public GoogleDistanceMatrixElement[] elements { get; set; }
    }

    public class GoogleDistanceMatrixElement
    {
        public string status { get; set; }
        public GoogleDistanceMatrixDistance distance { get; set; }
        public GoogleDistanceMatrixDuration duration { get; set; }
    }

    public class GoogleDistanceMatrixDistance
    {
        public string text { get; set; }
        public int value { get; set; }
    }

    public class GoogleDistanceMatrixDuration
    {
        public string text { get; set; }
        public int value { get; set; }
    }
}
}
