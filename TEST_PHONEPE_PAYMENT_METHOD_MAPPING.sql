-- =============================================
-- Test Script for PhonePe Payment Method Mapping
-- Description: Test the database-driven payment method lookup functionality
-- Date: 2025-07-19
-- =============================================

USE [CabYaari]
GO

PRINT '=== Testing PhonePe Payment Method Mapping ==='
PRINT ''

-- Test 1: Verify RLT_PAYMENT_METHOD table has PhonePePaymentMethod column
PRINT '1. Checking RLT_PAYMENT_METHOD table structure:'
SELECT 
    PKID,
    Payment_Method_Name,
    PhonePePaymentMethod,
    Is_Active
FROM [dbo].[RLT_PAYMENT_METHOD]
ORDER BY PKID
PRINT ''

-- Test 2: Test the stored procedure with CREDIT_CARD (from your example)
PRINT '2. Testing stored procedure with CREDIT_CARD (your example):'
DECLARE @TestBookingId nvarchar(30) = 'CY-170725-95186'
DECLARE @TestPaymentId nvarchar(30) = 'OM2507191244501182375267'
DECLARE @TestOrderId nvarchar(30) = 'OMO2507191244501172375404'
DECLARE @TestPhonePeMethod nvarchar(50) = 'CREDIT_CARD'

-- Check if the booking exists first
IF EXISTS (SELECT 1 FROM RLT_BOOKING WHERE Booking_Id = @TestBookingId)
BEGIN
    EXEC [dbo].[usp_Booking_Update_V2] 
        @BookingId = @TestBookingId,
        @RazorpayPaymentId = @TestPaymentId,
        @RazorpayOrderid = @TestOrderId,
        @RazorpaySignature = '',
        @RazorpayStatus = 'COMPLETED',
        @PaymentType = 'FULL',
        @PartialPaymentAmount = NULL,
        @RemainingAmountForDriver = 0.00,
        @PhonePePaymentMethod = @TestPhonePeMethod
    
    -- Verify the result
    SELECT 
        Booking_Id,
        Mode_Of_Payment_Id,
        razorpay_payment_id,
        razorpay_status,
        PaymentType
    FROM RLT_BOOKING 
    WHERE Booking_Id = @TestBookingId
    
    PRINT 'Expected: Mode_Of_Payment_Id should be 2 (Credit Card)'
END
ELSE
BEGIN
    PRINT 'Booking ' + @TestBookingId + ' not found. Creating test scenarios with different payment methods:'
END
PRINT ''

-- Test 3: Test payment method lookup logic
PRINT '3. Testing payment method lookup logic:'

-- Test CREDIT_CARD lookup
DECLARE @TestModeId int
SELECT TOP 1 @TestModeId = PKID
FROM [dbo].[RLT_PAYMENT_METHOD]
WHERE PhonePePaymentMethod = 'CREDIT_CARD'
AND Is_Active = 1

PRINT 'CREDIT_CARD maps to Mode_Of_Payment_Id: ' + ISNULL(CAST(@TestModeId AS nvarchar(10)), 'NULL')

-- Test UPI lookup
SELECT TOP 1 @TestModeId = PKID
FROM [dbo].[RLT_PAYMENT_METHOD]
WHERE PhonePePaymentMethod = 'UPI'
AND Is_Active = 1

PRINT 'UPI maps to Mode_Of_Payment_Id: ' + ISNULL(CAST(@TestModeId AS nvarchar(10)), 'NULL')

-- Test DEBIT_CARD lookup
SELECT TOP 1 @TestModeId = PKID
FROM [dbo].[RLT_PAYMENT_METHOD]
WHERE PhonePePaymentMethod = 'DEBIT_CARD'
AND Is_Active = 1

PRINT 'DEBIT_CARD maps to Mode_Of_Payment_Id: ' + ISNULL(CAST(@TestModeId AS nvarchar(10)), 'NULL')

-- Test fallback logic for unknown payment method
SET @TestModeId = NULL
SELECT TOP 1 @TestModeId = PKID
FROM [dbo].[RLT_PAYMENT_METHOD]
WHERE PhonePePaymentMethod = 'UNKNOWN_METHOD'
AND Is_Active = 1

IF @TestModeId IS NULL
BEGIN
    -- Test fallback to CARD for CREDIT_CARD/DEBIT_CARD
    SELECT TOP 1 @TestModeId = PKID
    FROM [dbo].[RLT_PAYMENT_METHOD]
    WHERE PhonePePaymentMethod = 'CARD'
    AND Is_Active = 1
    
    IF @TestModeId IS NULL
    BEGIN
        SET @TestModeId = 4  -- Default to Online Payment
    END
END

PRINT 'UNKNOWN_METHOD fallback maps to Mode_Of_Payment_Id: ' + ISNULL(CAST(@TestModeId AS nvarchar(10)), 'NULL')
PRINT ''

-- Test 4: Verify all expected mappings exist
PRINT '4. Verifying all expected PhonePe payment method mappings exist:'

DECLARE @MissingMappings TABLE (ExpectedMethod nvarchar(50))

-- Check for expected mappings
IF NOT EXISTS (SELECT 1 FROM [dbo].[RLT_PAYMENT_METHOD] WHERE PhonePePaymentMethod = 'CREDIT_CARD' AND Is_Active = 1)
    INSERT INTO @MissingMappings VALUES ('CREDIT_CARD')

IF NOT EXISTS (SELECT 1 FROM [dbo].[RLT_PAYMENT_METHOD] WHERE PhonePePaymentMethod = 'DEBIT_CARD' AND Is_Active = 1)
    INSERT INTO @MissingMappings VALUES ('DEBIT_CARD')

IF NOT EXISTS (SELECT 1 FROM [dbo].[RLT_PAYMENT_METHOD] WHERE PhonePePaymentMethod = 'UPI' AND Is_Active = 1)
    INSERT INTO @MissingMappings VALUES ('UPI')

IF NOT EXISTS (SELECT 1 FROM [dbo].[RLT_PAYMENT_METHOD] WHERE PhonePePaymentMethod = 'NET_BANKING' AND Is_Active = 1)
    INSERT INTO @MissingMappings VALUES ('NET_BANKING')

IF NOT EXISTS (SELECT 1 FROM [dbo].[RLT_PAYMENT_METHOD] WHERE PhonePePaymentMethod = 'WALLET' AND Is_Active = 1)
    INSERT INTO @MissingMappings VALUES ('WALLET')

IF NOT EXISTS (SELECT 1 FROM [dbo].[RLT_PAYMENT_METHOD] WHERE PhonePePaymentMethod = 'CARD' AND Is_Active = 1)
    INSERT INTO @MissingMappings VALUES ('CARD')

IF NOT EXISTS (SELECT 1 FROM [dbo].[RLT_PAYMENT_METHOD] WHERE PhonePePaymentMethod = 'ACCOUNT' AND Is_Active = 1)
    INSERT INTO @MissingMappings VALUES ('ACCOUNT')

IF EXISTS (SELECT 1 FROM @MissingMappings)
BEGIN
    PRINT 'WARNING: Missing PhonePe payment method mappings:'
    SELECT ExpectedMethod FROM @MissingMappings
END
ELSE
BEGIN
    PRINT 'SUCCESS: All expected PhonePe payment method mappings are present!'
END

PRINT ''
PRINT '=== Test Complete ==='

-- Summary of what should happen with your example:
PRINT ''
PRINT 'SUMMARY FOR YOUR EXAMPLE:'
PRINT 'PhonePe Response: "type": "CREDIT_CARD"'
PRINT 'Expected Database Lookup: PhonePePaymentMethod = "CREDIT_CARD"'
PRINT 'Expected Result: Mode_Of_Payment_Id = 2 (Credit Card)'
PRINT 'This will be automatically handled by the stored procedure when you process the payment.'
