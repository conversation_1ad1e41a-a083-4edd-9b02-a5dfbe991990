{
  "UseInMemoryDatabase": false,
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=localhost,1433;Initial Catalog=CabYaari;user id=cabyaari_user;password=********************************;TrustServerCertificate=True;",
    "IdentityConnection": "Data Source=localhost,1433;Initial Catalog=CabYaari;user id=cabyaari_user;password=********************************;TrustServerCertificate=True;"
    //"IdentityConnection": "Data Source=DESKTOP-ATE25CT\\SQLEXPRESS;Initial Catalog=identityDb;Integrated Security=True;MultipleActiveResultSets=True"
  },
  "Serilog": {
    "Using": [],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "Properties": {
      "ApplicationName": "Serilog.WebApplication"
    }
  },
  "MailSettings": {
    "EmailFrom": "<EMAIL>",
    "SmtpHost": "mail.smtp2go.com",
    "SmtpPort": 587,
    "SmtpUser": "cabyaari",
    "SmtpPass": "S2FxtIeJHloL2nqC",
    "DisplayName": "CabYaari"
  },
  "MapSettings": {
    "GrantType": "client_credentials",
    "ClientId": "fZqsZfvayHDeoMKEwvq1oa5lJvOJb0s7ByZio2GKFKR3CHrYqZAlxgSwdVXqfUdAafuZVGYkxNlZ2KTY-0Gw_g==",
    "ClientSecret": "9K_q_9Q2GHP3JWfb2e8giYxXMbpCGf6UHJxp19Tkw4cv8-p5wwzP8amxlGUEKg4-flfQKMyVuMwZ02xgI49Ej71luFNwWORd",
    "RestAPIKey": "lynngnrhhwp9xmnj4qzhccid36clntgl",
    "MapAPIKey": "4f8veyov8dg5y3zfsz84npsqv5nua3rd",
    "OAuthAPI": "https://outpost.mapmyindia.com/api/security/oauth/token?",
    "AutoSuggestAPI": "https://atlas.mapmyindia.com/api/places/search/json?query=",
    "NearByAPI": "https://atlas.mapmyindia.com/api/places/nearby/json?explain&richData",
    "GeocodeAPI": "https://atlas.mapmyindia.com/api/places/geocode?address=mapmyindia237okhlaphase3",
    "TextSearchAPI": "https://atlas.mapmyindia.com/api/places/textsearch/json?query=",
    "DistnaceMatrixAPI": "https://apis.mapmyindia.com/advancedmaps/v1/",
    "EtaAPI": "https://apis.mapmyindia.com/advancedmaps/v1/lynngnrhhwp9xmnj4qzhccid36clntgl/route_eta/driving/",
    "GetCityAPI": "https://atlas.mapmyindia.com/api/places/textsearch/json?query="
  },
  "PaymentSettings": {
    "PaymentSecret": "4wBCZ6rwTIRqgZ3Ta1mvWKdh",
    "PaymentKey": "rzp_test_ZyDQmRRFvaTTva",
    "PaymentAPI": "",
    "PhonePe": {
      "ApiKey": "TEST-M232TTWKCYF8S_25060",
      //"ApiKey": "SU2506021350482179239732",
      "ApiSecret": "ZDQ3NTVkNDktZGUzYy00MjBjLTg5MDAtYjkzY2QwOWYwNjVi",
      //"ApiSecret": "d1a2dcf7-bef5-4fdf-b931-1b11baa3cc62",
      "SaltKey": "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399",
      "Environment": "TEST",
      //"Environment": "Production",
      "ApiUrl": "https://api-preprod.phonepe.com/apis/pg-sandbox",
      //"ApiUrl": "https://api.phonepe.com/apis/pg",
      //"oauthApiUrl": "https://api.phonepe.com/apis/identity-manager",
      "oauthApiUrl": "https://api-preprod.phonepe.com/apis/pg-sandbox",
      "CallbackUrl": "https://api.cabyaari.com/api/v1/payments/phonepe/callback",
      "RedirectUrl": "https://cabyaari.com/#/payment-callback"
    }
  },
  "AnonymousPaymentSettings": {
    "EncryptionPassphrase": "CabYaari@2024#AnonymousPayment!SecureKey",
    "PaymentLinkExpiryHours": 24,
    "CallbackUrl": "https://cabyaari.com/#/anonymous-payment-callback"
  },
  "SmsSettings": {
    "OTPAPI": "https://api.textlocal.in/send/",
    "OTPAPIKey": "Pvkx1uQ4dc4-7mwQCRHsolxusKZArop6ib7OPOzev5",
    "Sender": "Oneway"
  },
  "ICloudSmsSettings": {
    "ApiUrl": "http://msg.icloudsms.com/rest/services/sendSMS/sendGroupSms",
    "AuthKey": "bd937e65947c6aca90ed26ecd15667b9",
    "SenderId": "OWTCPL",
    "RouteId": "8",
    "SmsContentType": "english",
    "TmId": "140200000022",
    "ConcentFailoverId": "30",
    "AdminAlertNumber": "9871516348"
  },
  "JWTSettings": {
    "Key": "9K_q_9Q2GHP3JWfb2e8giYxXMbpCGf6UHJxp19Tkw4cv8-p5wwzP8amxlGUEKg4-flfQKMyVuMwZ02xgI49Ej71luFNwWORd",
    "Issuer": "CoreIdentity",
    "Audience": "CoreIdentityUser",
    "DurationInMinutes": 60
  },
  "AllowedHosts": "*",
  "GoogleMaps": {
    "ApiKey": "AIzaSyDM8oBKuplVyAEEzxpWnxaIFQApDzkrn0Q"
  },
  "FareSettings": {
    "DetourBufferKM": 20,
    "TimeCompensationSpeedThresholdKmh": 50,
    "TimeCompensationStandardSpeedKmh": 60,
    "TimeBufferMinutes": 5,
    "ShortDistanceThresholdKM": 200,
    "ShortDistanceRateAdjustment": 1.0
  }
}
