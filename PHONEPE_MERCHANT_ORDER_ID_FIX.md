# PhonePe MerchantOrderId Fix for Anonymous Payments

## Issue Identified
The anonymous payment verification was failing with HTTP 204 (NoContent) because of incorrect ID usage in the PhonePe API calls.

## Root Cause Analysis

### Problem
The anonymous payment system was using different ID patterns compared to the working regular payment system:

**Anonymous Payment (Incorrect)**:
- `MerchantOrderId`: `ORDER_ANON_1753022725` (generated ID)
- `MerchantTransactionId`: `ANON_TXN_1753022725` (generated ID)
- **Verification**: Using `ANON_TXN_1753022725` → **404/NoContent**

**Regular Payment (Working)**:
- `MerchantOrderId`: `CY-200725-85918` (actual booking ID)
- `MerchantTransactionId`: `CY-200725-85918` (actual booking ID)
- **Verification**: Using `CY-200725-85918` → **Success**

### PhonePe API Expectation
PhonePe expects the **actual booking ID** as the `MerchantOrderId` for both initiation and verification, not generated transaction IDs.

## Solution Applied

### 1. **Fixed Payment Initiation**
**File**: `Application/Features/Payments/AnonymousPayments/InitiateAnonymousPayment.cs`

**Before (Incorrect)**:
```csharp
var merchantTransactionId = $"ANON_TXN_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}";
var orderId = $"ORDER_ANON_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}";

var paymentRequest = new PhonePePaymentInitiateRequest
{
    MerchantOrderId = orderId, // ❌ Using generated ID
    Amount = amountInPaise,
    // ...
};
```

**After (Fixed)**:
```csharp
var merchantTransactionId = $"ANON_TXN_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}";
var merchantOrderId = bookingId; // ✅ Use actual booking ID

var paymentRequest = new PhonePePaymentInitiateRequest
{
    MerchantOrderId = merchantOrderId, // ✅ Use actual booking ID
    Amount = amountInPaise,
    // ...
};
```

### 2. **Fixed Response Structure**
**Before (Incorrect)**:
```csharp
var result = new AnonymousPaymentInitiateResponse
{
    TokenUrl = phonePeResponse.RedirectUrl,
    MerchantTransactionId = merchantTransactionId, // ❌ Generated ID
    OrderId = orderId, // ❌ Our generated ID
    // ...
};
```

**After (Fixed)**:
```csharp
var result = new AnonymousPaymentInitiateResponse
{
    TokenUrl = phonePeResponse.RedirectUrl,
    MerchantTransactionId = merchantOrderId, // ✅ Booking ID (for verification)
    OrderId = phonePeResponse.OrderId, // ✅ PhonePe's order ID
    // ...
};
```

### 3. **Updated Verification Logic**
The verification now uses the booking ID (passed as MerchantTransactionId) which matches the working implementation pattern.

## Expected Flow After Fix

### 1. **Payment Initiation**
```json
// Request
{
  "bookingId": "encrypted_booking_id",
  "amountToPay": 20983
}

// Response
{
  "succeeded": true,
  "data": {
    "tokenUrl": "https://mercury-uat.phonepe.com/transact/...",
    "merchantTransactionId": "CY-200725-85918", // ✅ Actual booking ID
    "orderId": "OMO2507202011378552375576", // ✅ PhonePe's order ID
    "bookingId": "encrypted_booking_id",
    "amount": 20983
  }
}
```

### 2. **Payment Verification**
```json
// Request (Frontend should use the returned values)
{
  "merchantTransactionId": "CY-200725-85918", // ✅ Use booking ID
  "orderId": "OMO2507202011378552375576", // ✅ Use PhonePe order ID
  "bookingId": "encrypted_booking_id"
}

// Expected: HTTP 200 with payment details (not 204 NoContent)
```

## Key Changes Summary

### InitiateAnonymousPayment.cs
✅ **MerchantOrderId**: Changed from generated ID to actual booking ID
✅ **Response**: Returns booking ID as MerchantTransactionId for verification
✅ **Response**: Returns PhonePe's order ID instead of our generated ID
✅ **Logging**: Added detailed logging for debugging

### VerifyAnonymousPayment.cs
✅ **Endpoint**: Now uses booking ID (passed as MerchantTransactionId)
✅ **Error Handling**: Added better handling for empty/non-JSON responses
✅ **Logging**: Added detailed logging for debugging

## Testing the Fix

### 1. **Initiate Payment**
```bash
POST /api/v1/anonymous-payments/initiate
# Should return booking ID as merchantTransactionId
```

### 2. **Verify Payment** (Use returned values from initiation)
```bash
POST /api/v1/anonymous-payments/verify
{
  "merchantTransactionId": "CY-200725-85918", // From initiation response
  "orderId": "OMO2507202011378552375576", // From initiation response
  "bookingId": "encrypted_booking_id"
}
# Should return HTTP 200 with payment details (not 204)
```

## Frontend Integration Note

The frontend should use the exact values returned from the payment initiation response for verification:

```javascript
// After payment initiation
const initiateResponse = await initiatePayment(paymentData);
const { merchantTransactionId, orderId, bookingId } = initiateResponse.data;

// For payment verification (after user completes payment)
const verifyRequest = {
  merchantTransactionId, // Use the returned booking ID
  orderId, // Use the returned PhonePe order ID
  bookingId // Use the encrypted booking ID
};
```

## Files Updated
- `Application/Features/Payments/AnonymousPayments/InitiateAnonymousPayment.cs`
- `Application/Features/Payments/AnonymousPayments/VerifyAnonymousPayment.cs`

The anonymous payment system now follows the same ID pattern as the working regular payment system.
