# Partial Payment Test Scenarios

## Test Data
Based on the provided booking data:
- PKID: 468
- Booking_Id: CY-260725-75274
- PaymentType: PARTIAL
- PartialPaymentAmount: 14360.00
- RemainingAmountForDriver: 45952.00
- Fare: 60312.00

## Test Scenarios

### 1. Get Anonymous Booking Details - Partial Payment

**Request:**
```
GET /api/v1/anonymous-payments/booking/{encrypted_booking_id}
```

**Expected Response Fields:**
```json
{
  "data": {
    "paymentType": "PARTIAL",
    "partialPaymentAmount": 14360.00,
    "remainingAmountForDriver": 45952.00,
    "isPartialPayment": true,
    "onlinePaymentAmount": 14360.00,
    "driverPaymentAmount": 45952.00,
    "totalFare": 60312.00
  }
}
```

### 2. Get Anonymous Booking Details - Full Payment

**Test with a full payment booking:**
```json
{
  "data": {
    "paymentType": "FULL",
    "partialPaymentAmount": null,
    "remainingAmountForDriver": null,
    "isPartialPayment": false,
    "onlinePaymentAmount": 60312.00,
    "driverPaymentAmount": 0.00,
    "totalFare": 60312.00
  }
}
```

### 3. Initiate Payment - Partial Payment (Correct Amount)

**Request:**
```json
{
  "bookingId": "encrypted_booking_id",
  "paymentOption": 2,
  "amountToPay": 14360.00,
  "callbackUrl": "https://cabyaari.com/#/anonymous-payment-callback"
}
```

**Expected:** Success - Payment initiation should work

### 4. Initiate Payment - Partial Payment (Wrong Amount)

**Request:**
```json
{
  "bookingId": "encrypted_booking_id",
  "paymentOption": 2,
  "amountToPay": 60312.00,
  "callbackUrl": "https://cabyaari.com/#/anonymous-payment-callback"
}
```

**Expected:** Error - "Payment amount mismatch for partial payment. Expected: 14360, Provided: 60312"

### 5. Verify Payment - Partial Payment

**Request:**
```json
{
  "merchantTransactionId": "CY-260725-75274",
  "orderId": "phonepe_order_id",
  "bookingId": "encrypted_booking_id"
}
```

**Expected Response:**
```json
{
  "data": {
    "paymentStatus": "COMPLETED",
    "amount": 14360.00,
    "partialPaymentAmount": 14360.00,
    "remainingAmountForDriver": 45952.00
  }
}
```

## Frontend Test Cases

### 1. Payment Button Text
- Partial Payment: "Pay ₹14,360 Online"
- Full Payment: "Pay ₹60,312"

### 2. Payment Summary Display
- Partial Payment: Show breakdown with online and driver amounts
- Full Payment: Show single total amount

### 3. Success Message
- Partial Payment: "Partial payment of ₹14,360 completed. Please pay ₹45,952 to the driver."
- Full Payment: "Payment of ₹60,312 completed successfully."

## Database Verification

After payment completion, verify in database:
```sql
SELECT 
    Booking_Id,
    PaymentType,
    PartialPaymentAmount,
    RemainingAmountForDriver,
    Fare,
    razorpay_status
FROM RLT_BOOKING 
WHERE Booking_Id = 'CY-260725-75274'
```

Expected values:
- PaymentType: 'PARTIAL'
- PartialPaymentAmount: 14360.00
- RemainingAmountForDriver: 45952.00
- razorpay_status: 'Paid' (after successful payment)

## Error Scenarios to Test

### 1. Invalid Payment Amount
- Try to pay full fare for partial payment booking
- Try to pay partial amount for full payment booking

### 2. Missing Payment Type
- Booking with NULL PaymentType should default to "FULL"

### 3. Invalid Booking ID
- Non-existent booking should return appropriate error

### 4. Non-Admin Booking
- Regular user booking should not be accessible via anonymous payment

## Console Logs to Monitor

The following console logs will help debug:
```
[GetAnonymousBookingDetails] Payment Type: PARTIAL
[GetAnonymousBookingDetails] Is Partial Payment: true
[GetAnonymousBookingDetails] Online Payment Amount: 14360
[GetAnonymousBookingDetails] Driver Payment Amount: 45952

[InitiateAnonymousPayment] Payment Type: PARTIAL
[InitiateAnonymousPayment] Is Partial Payment: true
[InitiateAnonymousPayment] Expected Amount: 14360
[InitiateAnonymousPayment] Requested Amount: 14360

[VerifyAnonymousPayment] Payment Type: PARTIAL
[VerifyAnonymousPayment] Is Partial Payment: true
[VerifyAnonymousPayment] Partial Payment Amount: 14360
[VerifyAnonymousPayment] Remaining Amount for Driver: 45952
```
