# Car Capacity and Features Database Fix

## Issue Identified
The anonymous payment system was using hardcoded values for car capacity and features:
- ❌ **CarCapacity**: Hardcoded to `4`
- ❌ **CarFeatures**: Hardcoded to `"AC, Music System, GPS Tracking"`

## Solution Applied
Updated the system to fetch actual car category details from the database, following the same pattern used in the existing `GetSelectedtRouteCategoryFareDetails` API.

## Database Query Used
Based on the existing implementation in `GetSelectedRouteCategoryFareDetails.cs`:

```sql
SELECT 
    PKID,
    Car_Category_Abbr as CategoryName,
    Features,
    Capacity,
    Car_Categroy_Image as CategoryImage,
    Per_KM_fare as PerKMCharges, 
    Base_Fare as BaseFare 
FROM RLT_CAR_CATEGORY 
WHERE Is_Active = 1 AND Car_Category_Abbr = @CategoryName
```

## Files Updated

### 1. **Repository Interface**
**File**: `Application/Interfaces/Repositories/IBookingRepositoryAsync.cs`
- Added: `Task<CarCategoryDetails> GetCarCategoryDetailsAsync(string categoryName);`

### 2. **Repository Implementation**
**File**: `Infrastructure.Persistence/Repositories/BookingRepositoryAsync.cs`
- Added: `GetCarCategoryDetailsAsync()` method
- Uses the same SQL query as the working fare details API

### 3. **DTO Creation**
**File**: `Application/DTOs/Payment/AnonymousPaymentDto.cs`
- Added: `CarCategoryDetails` class with all necessary properties

### 4. **Anonymous Booking Details**
**File**: `Application/Features/Payments/AnonymousPayments/GetAnonymousBookingDetails.cs`

**Before (Hardcoded)**:
```csharp
CarFeatures = "AC, Music System, GPS Tracking", // Default features
CarCapacity = 4, // Default capacity
```

**After (Database-driven)**:
```csharp
// Get car category details from database
var carCategoryDetails = await _bookingRepository.GetCarCategoryDetailsAsync(booking.CarCategory);

CarFeatures = carCategoryDetails?.Features ?? "AC, Music System, GPS Tracking", // Use DB features or fallback
CarCapacity = carCategoryDetails?.Capacity ?? 4, // Use DB capacity or fallback
```

### 5. **Anonymous Payment Receipt**
**File**: `Application/Features/Payments/AnonymousPayments/GetAnonymousPaymentReceipt.cs`
- Applied the same database-driven approach for receipt generation

## Expected Results

### For SUV Premium (from your API example):
```json
{
  "carCategory": "SUV Premium",
  "carFeatures": "Innova Crysta", // ✅ From database
  "carCapacity": 6, // ✅ From database (not hardcoded 4)
  "perKMCharges": "16.00",
  // ... other fields
}
```

### For Other Categories:
The system will now dynamically fetch:
- **Sedan**: Capacity 4, Features from DB
- **Hatchback**: Capacity 4, Features from DB  
- **SUV**: Capacity 7, Features from DB
- **SUV Premium**: Capacity 6, Features "Innova Crysta"

## Fallback Mechanism
If car category details are not found in the database:
- **Capacity**: Falls back to `4`
- **Features**: Falls back to `"AC, Music System, GPS Tracking"`

This ensures the system remains functional even if there are data issues.

## Logging Added
Added detailed logging to track:
- Car category being queried
- Whether car details were found
- Actual capacity and features retrieved

Example logs:
```
[GetAnonymousBookingDetails] Car Category: SUV Premium
[GetAnonymousBookingDetails] Car Details Found: True
[GetAnonymousBookingDetails] Capacity: 6, Features: Innova Crysta
```

## Database Table Structure
The fix uses the existing `RLT_CAR_CATEGORY` table:
- **Car_Category_Abbr**: Category name (e.g., "SUV Premium")
- **Capacity**: Number of passengers
- **Features**: Car features/model description
- **Per_KM_fare**: Rate per kilometer
- **Base_Fare**: Base fare amount

## Testing Verification

### Test Cases:
1. **SUV Premium**: Should show Capacity=6, Features="Innova Crysta"
2. **Sedan**: Should show actual capacity and features from DB
3. **Invalid Category**: Should use fallback values (Capacity=4, default features)

### API Endpoints to Test:
- `GET /api/v1/anonymous-payments/booking/{bookingId}` - Check booking details
- `GET /api/v1/anonymous-payments/receipt/{bookingId}` - Check receipt details

Both should now show the correct car capacity and features from the database instead of hardcoded values.

## Benefits
✅ **Accurate Information**: Shows actual car capacity and features
✅ **Consistency**: Matches the existing fare details API
✅ **Maintainable**: Changes to car details in DB automatically reflect in anonymous payments
✅ **Robust**: Fallback mechanism prevents system failures
