# Payment Option Mapping Final Update

## Overview
Updated the PaymentOption mapping logic to ensure consistency between PaymentType string and PaymentOption integer values across all payment flows.

## Final PaymentOption Mapping

### PaymentOption Enum (Kept Original)
**File:** `Application/Enums/PaymentOption.cs`

```csharp
public enum PaymentOption
{
    PartialPay=1,     // Partial payment (online + cash to driver)
    FullPay=2,        // Full online payment
    FullPayToDriver=3 // Full payment to driver in cash
}
```

## Changes Made

### 1. Updated AutoMapper Configuration
**File:** `Application/Mappings/GeneralProfile.cs`

**Added custom mapping logic:**
```csharp
CreateMap<BookingCommand, RLT_BOOKING>()
    .ForMember(dest => dest.PaymentOption, opt => opt.MapFrom(src => MapPaymentTypeToPaymentOption(src.PaymentType)));

private static int MapPaymentTypeToPaymentOption(string paymentType)
{
    return paymentType?.ToUpper() switch
    {
        "PARTIAL" => (int)PaymentOption.PartialPay,  // 1 = Partial payment
        "FULL" => (int)PaymentOption.FullPay,        // 2 = Full online payment
        _ => (int)PaymentOption.FullPay               // Default to full payment (2)
    };
}
```

### 2. Enhanced Anonymous Payment Validation
**File:** `Application/Features/Payments/AnonymousPayments/InitiateAnonymousPayment.cs`

**Added PaymentOption validation using enum values:**
```csharp
// Validate PaymentOption matches the booking's PaymentType
var expectedPaymentOption = isPartialPayment ? (int)PaymentOption.PartialPay : (int)PaymentOption.FullPay;
if (request.Request.PaymentOption != expectedPaymentOption)
{
    var paymentTypeText = isPartialPayment
        ? $"partial payment (PaymentOption={PaymentOption.PartialPay})"
        : $"full payment (PaymentOption={PaymentOption.FullPay})";
    throw new ApiException($"PaymentOption mismatch. This booking requires {paymentTypeText}. Provided: {request.Request.PaymentOption}");
}
```

### 3. Updated Stored Procedures
**File:** `STORED_PROCEDURES_V2.sql`

#### Booking Creation Procedure (usp_Booking_Create_V2):
```sql
-- Set PaymentOption based on PaymentType if not provided or incorrect
-- PaymentOption: 1=PartialPay, 2=FullPay, 3=FullPayToDriver
IF @PaymentOption IS NULL OR @PaymentOption = 0
BEGIN
    SET @PaymentOption = CASE 
        WHEN @PaymentType = 'PARTIAL' THEN 1  -- PartialPay
        WHEN @PaymentType = 'FULL' THEN 2     -- FullPay
        ELSE 2  -- Default to FullPay
    END
END
```

#### Booking Update Procedure (usp_Booking_Update_V2):
```sql
-- Determine the correct PaymentOption based on PaymentType
DECLARE @CorrectPaymentOption int
SET @CorrectPaymentOption = CASE 
    WHEN ISNULL(@PaymentType, @CurrentPaymentType) = 'PARTIAL' THEN 1  -- PartialPay
    WHEN ISNULL(@PaymentType, @CurrentPaymentType) = 'FULL' THEN 2     -- FullPay
    ELSE 2  -- Default to FullPay
END

-- Update PaymentOption to match PaymentType
PaymentOption = @CorrectPaymentOption,
```

## Final PaymentOption Values

| Value | Enum Name | PaymentType | Description | Usage |
|-------|-----------|-------------|-------------|-------|
| 1 | PartialPay | "PARTIAL" | Partial payment | Customer pays partial amount online, remaining to driver |
| 2 | FullPay | "FULL" | Full online payment | Customer pays entire amount online via PhonePe |
| 3 | FullPayToDriver | N/A | Full payment to driver | Customer pays entire amount in cash to driver |

## Database Alignment

This change ensures consistency where:
- **PaymentType = "PARTIAL"** → **PaymentOption = 1**
- **PaymentType = "FULL"** → **PaymentOption = 2**

## Impact on Both Payment Flows

### ✅ Anonymous Payment Flow:
- Validates PaymentOption matches booking's PaymentType
- Rejects mismatched PaymentOption values
- Ensures consistency with existing booking data

### ✅ Regular PhonePe Payment Flow:
- AutoMapper automatically sets correct PaymentOption based on PaymentType
- Stored procedure validates and corrects PaymentOption if needed
- Database stores consistent values

## Testing Recommendations

1. **Test Partial Payment Flow:**
   - PaymentType = "PARTIAL" should set PaymentOption = 1
   - Anonymous payment with PaymentOption = 2 should be rejected for partial bookings

2. **Test Full Payment Flow:**
   - PaymentType = "FULL" should set PaymentOption = 2
   - Anonymous payment with PaymentOption = 1 should be rejected for full bookings

3. **Test Database Consistency:**
   - Verify all new bookings have matching PaymentType and PaymentOption
   - Check that updates maintain consistency

## Summary

The system now ensures:
- **PaymentOption = 1** for **partial payments** (PaymentType = "PARTIAL")
- **PaymentOption = 2** for **full payments** (PaymentType = "FULL")

Both anonymous payment flow and regular PhonePe payment flow will maintain this consistency automatically.
