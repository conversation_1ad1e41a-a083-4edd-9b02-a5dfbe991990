-- =============================================
-- Database Update Script: Add Partial Payment Support to User Bookings API
-- Description: Updates the user bookings stored procedure to include partial payment fields
-- Author: CabYaari Development Team
-- Created: 2025-07-30
-- =============================================

USE [CabYaari]
GO

-- First, create the new stored procedure with partial payment support
-- =============================================
-- Stored Procedure: usp_Booking_GetUserBookings_V4
-- Description: Enhanced version with partial payment support
-- =============================================

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- Drop existing procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'usp_Booking_GetUserBookings_V4')
BEGIN
    DROP PROCEDURE [dbo].[usp_Booking_GetUserBookings_V4]
END
GO

CREATE PROCEDURE [dbo].[usp_Booking_GetUserBookings_V4]
    @UserId NVARCHAR(450),  -- This will be the username from JWT token
    @Cursor NVARCHAR(50) = NULL,
    @PageSize INT = 10
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    -- Validate page size (max 10)
    IF @PageSize > 10
        SET @PageSize = 10;
    
    IF @PageSize <= 0
        SET @PageSize = 10;

    PRINT '[DEBUG] Input parameters - UserId: ' + ISNULL(@UserId, 'NULL') + ', Cursor: ' + ISNULL(@Cursor, 'NULL') + ', PageSize: ' + CAST(@PageSize AS NVARCHAR(10));

    -- The UserId parameter can be either a username or a GUID
    -- Let's determine which one it is and get the appropriate GUID
    DECLARE @UserGuid NVARCHAR(450) = NULL;

    -- First, check if the UserId is already a GUID (36 characters with hyphens)
    IF LEN(@UserId) = 36 AND @UserId LIKE '%-%-%-%-%'
    BEGIN
        -- It's likely a GUID, use it directly
        SET @UserGuid = @UserId;
        PRINT '[DEBUG] UserId appears to be a GUID, using directly: ' + @UserGuid;
    END
    ELSE
    BEGIN
        -- It's likely a username, look up the GUID
        SELECT @UserGuid = Id
        FROM [Identity].[User]
        WHERE UserName = @UserId;

        IF @UserGuid IS NULL
        BEGIN
            PRINT '[DEBUG] User not found with username: ' + @UserId;
            -- Return empty result set with all fields including new ones
            SELECT
                '' as BookingId, '' as PickUpCity, '' as DropOffCity, '' as TripType,
                '' as CarCategory, 0 as Fare, '' as PickUpAddress, '' as DropOffAddress,
                NULL as PickUpDate, '' as PickUpTime, '' as TravelerName, '' as PhoneNumber,
                '' as RazorpayStatus, NULL as BookingDate, 0 as IsPartialPayment, 0 as PaidAmount
            WHERE 1 = 0;
            RETURN;
        END

        PRINT '[DEBUG] Found user GUID: ' + @UserGuid + ' for username: ' + @UserId;
    END

    -- Parse cursor - can be either a date or a BookingId
    DECLARE @CursorDate DATETIME = NULL;
    DECLARE @CursorBookingId NVARCHAR(50) = NULL;
    
    IF @Cursor IS NOT NULL AND @Cursor != ''
    BEGIN
        -- Try to parse as date first
        BEGIN TRY
            SET @CursorDate = CAST(@Cursor AS DATETIME);
            PRINT '[DEBUG] Cursor parsed as date: ' + CONVERT(NVARCHAR(30), @CursorDate, 121);
        END TRY
        BEGIN CATCH
            -- If not a date, treat as BookingId
            SET @CursorBookingId = @Cursor;
            PRINT '[DEBUG] Cursor treated as BookingId: ' + @CursorBookingId;
        END CATCH
    END
    ELSE
    BEGIN
        PRINT '[DEBUG] No cursor provided - first page request';
    END

    -- Debug: Check how many bookings this user has
    DECLARE @TotalBookings INT;
    SELECT @TotalBookings = COUNT(*)
    FROM [dbo].[RLT_BOOKING]
    WHERE Booking_Created_By = @UserGuid;

    PRINT '[DEBUG] Using UserGuid for lookup: ' + ISNULL(@UserGuid, 'NULL');
    PRINT '[DEBUG] Total bookings for user: ' + CAST(@TotalBookings AS NVARCHAR(10));

    -- Get bookings with pagination including partial payment fields
    WITH BookingData AS (
        SELECT
            b.Booking_Id as BookingId,
            cf.CitY_Name as PickUpCity,
            ct.CitY_Name as DropOffCity,
            tt.Trip_Type as TripType,
            cc.Car_Category_Abbr as CarCategory,
            -- For partial payments, fare should be Basic_Fare + GST (total fare)
            -- For full payments, use the existing Fare column
            CASE
                WHEN b.PaymentType = 'PARTIAL' THEN ROUND(ISNULL(b.Basic_Fare, 0) + ISNULL(b.GST, 0), 0)
                ELSE ROUND(ISNULL(b.Fare, 0), 0)
            END as Fare,
            b.PickUp_Address as PickUpAddress,
            b.DropOff_Address as DropOffAddress,
            b.PickUp_Date as PickUpDate,
            b.PickUp_Time as PickUpTime,
            b.[Name] as TravelerName,
            b.Mobile_No1 as PhoneNumber,
            b.razorpay_status as RazorpayStatus,
            b.Created_Date as BookingDate,
            -- Add partial payment fields
            CASE WHEN b.PaymentType = 'PARTIAL' THEN 1 ELSE 0 END as IsPartialPayment,
            CASE
                WHEN b.PaymentType = 'PARTIAL' THEN ROUND(ISNULL(b.PartialPaymentAmount, 0), 0)
                ELSE ROUND(ISNULL(b.Fare, 0), 0)
            END as PaidAmount,
            ROW_NUMBER() OVER (ORDER BY 
                CASE WHEN b.Created_Date IS NOT NULL THEN b.Created_Date ELSE '1900-01-01' END DESC,
                b.Booking_Id DESC
            ) as RowNum
        FROM [dbo].[RLT_BOOKING] b
        INNER JOIN [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
        INNER JOIN [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
        INNER JOIN [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
        INNER JOIN [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
        WHERE b.Booking_Created_By = @UserGuid
        AND (
            -- Date-based cursor (preferred when BookingDate is available)
            (@CursorDate IS NOT NULL AND (b.Created_Date < @CursorDate OR b.Created_Date IS NULL))
            -- BookingId-based cursor (fallback when BookingDate is null)
            OR (@CursorBookingId IS NOT NULL AND b.Booking_Id < @CursorBookingId)
            -- No cursor (first page)
            OR (@CursorDate IS NULL AND @CursorBookingId IS NULL)
        )
    )
    SELECT TOP (@PageSize + 1) *
    FROM BookingData
    ORDER BY 
        CASE WHEN BookingDate IS NOT NULL THEN BookingDate ELSE '1900-01-01' END DESC,
        BookingId DESC;

    PRINT '[DEBUG] Query completed successfully';

END
GO

PRINT 'usp_Booking_GetUserBookings_V4 created successfully!'

-- =============================================
-- Test the new stored procedure
-- =============================================
PRINT 'Testing the new stored procedure...'

-- Test with a known user (replace with actual username)
-- EXEC [dbo].[usp_Booking_GetUserBookings_V4] @UserId = 'kunalsaini9', @PageSize = 5

PRINT 'Database update completed successfully!'
PRINT 'The user bookings API now supports partial payment fields:'
PRINT '- IsPartialPayment: Boolean flag indicating if booking is partial payment'
PRINT '- PaidAmount: Amount actually paid online (PartialPaymentAmount for partial, Fare for full)'
