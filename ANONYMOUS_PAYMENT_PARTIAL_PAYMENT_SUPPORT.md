# Anonymous Payment Partial Payment Support

## Overview
This document outlines the backend changes made to support partial payment details in the anonymous payment flow and provides guidance for frontend implementation.

## Backend Changes Made

### 1. Enhanced Anonymous Booking Details Response
**File:** `Application/DTOs/Payment/AnonymousPaymentDto.cs`

Added new fields to `AnonymousBookingDetailsResponse`:
```csharp
// Partial Payment Details
public string PaymentType { get; set; } // "PARTIAL" or "FULL"
public decimal? PartialPaymentAmount { get; set; }
public decimal? RemainingAmountForDriver { get; set; }
public bool IsPartialPayment { get; set; }
public decimal OnlinePaymentAmount { get; set; } // Amount to be paid online
public decimal DriverPaymentAmount { get; set; } // Amount to be paid to driver
```

### 2. Updated Get Anonymous Booking Details Handler
**File:** `Application/Features/Payments/AnonymousPayments/GetAnonymousBookingDetails.cs`

- Added logic to determine if booking is partial payment
- Calculates online and driver payment amounts
- Includes partial payment details in response

### 3. Enhanced Payment Initiation Handler
**File:** `Application/Features/Payments/AnonymousPayments/InitiateAnonymousPayment.cs`

- Updated payment amount validation to handle partial payments
- Uses `PartialPaymentAmount` for partial payments instead of total fare
- Added detailed logging for payment type detection

### 4. Updated Payment Verification Handler
**File:** `Application/Features/Payments/AnonymousPayments/VerifyAnonymousPayment.cs`

- Retrieves booking details to determine payment type
- Includes partial payment information in verification response
- Properly sets `PartialPaymentAmount` and `RemainingAmountForDriver`

## API Response Changes

### GET /api/v1/anonymous-payments/booking/{bookingId}

**Enhanced Response Structure:**
```json
{
  "succeeded": true,
  "message": null,
  "errors": null,
  "data": {
    "bookingId": "encrypted_booking_id",
    "tripType": "One Way",
    "pickUpCity": "Ahmedabad",
    "dropOffCity": "Aizawl",
    "pickUpAddress": "railway, Nagarvel Hanuman Road...",
    "dropOffAddress": "The Esquire, Mission Veng...",
    "pickUpDate": "2025-07-24",
    "pickUpTime": "19:04",
    "carCategory": "Sedan",
    "carFeatures": "AC, Music System, GPS Tracking",
    "carCapacity": 4,
    "distance": 3590.00,
    "duration": "59 hrs 45 mins",
    "basicFare": 57440.00,
    "gst": 2872.00,
    "totalFare": 60312.00,
    "perKMCharges": "15.99",
    "fixRateNote": "Fixed rate for Ahmedabad-Aizawl route",
    "tollCharge": 0.00,
    "travelerName": "Kunal",
    "phoneNumber": "8824516274",
    "mailId": "<EMAIL>",
    "paymentStatus": "PENDING",
    "isPaymentPending": true,
    "cashAmountToPayDriver": 45952.00,
    "bookingCreatedAt": "2025-07-26T19:05:33.32",
    "expiresAt": "2025-07-27T19:05:33.32",
    
    // NEW PARTIAL PAYMENT FIELDS
    "paymentType": "PARTIAL",
    "partialPaymentAmount": 14360.00,
    "remainingAmountForDriver": 45952.00,
    "isPartialPayment": true,
    "onlinePaymentAmount": 14360.00,
    "driverPaymentAmount": 45952.00
  }
}
```

### POST /api/v1/anonymous-payments/verify

**Enhanced Response Structure:**
```json
{
  "succeeded": true,
  "message": null,
  "errors": null,
  "data": {
    "paymentStatus": "COMPLETED",
    "transactionId": "CY-260725-75274",
    "orderId": "phonepe_order_id",
    "bookingId": "encrypted_booking_id",
    "amount": 14360.00,
    "paymentId": "phonepe_payment_id",
    "paymentType": "PhonePe",
    "paymentDate": "2025-07-26T19:30:00.000Z",
    
    // PARTIAL PAYMENT DETAILS
    "partialPaymentAmount": 14360.00,
    "remainingAmountForDriver": 45952.00,
    "receiptNumber": "RCP-260725-001"
  }
}
```

## Frontend Implementation Guide

### 1. Detecting Partial Payment Bookings

Check the `isPartialPayment` field in the booking details response:

```javascript
const bookingDetails = response.data;

if (bookingDetails.isPartialPayment) {
    // This is a partial payment booking
    console.log('Partial Payment Amount:', bookingDetails.onlinePaymentAmount);
    console.log('Driver Payment Amount:', bookingDetails.driverPaymentAmount);
} else {
    // This is a full payment booking
    console.log('Full Payment Amount:', bookingDetails.totalFare);
}
```

### 2. Displaying Payment Information

For partial payment bookings, show both amounts clearly:

```javascript
// Example UI structure
if (bookingDetails.isPartialPayment) {
    displayPaymentBreakdown({
        totalFare: bookingDetails.totalFare,
        onlinePayment: bookingDetails.onlinePaymentAmount,
        driverPayment: bookingDetails.driverPaymentAmount,
        paymentType: 'Partial Payment'
    });
} else {
    displayPaymentBreakdown({
        totalFare: bookingDetails.totalFare,
        onlinePayment: bookingDetails.totalFare,
        driverPayment: 0,
        paymentType: 'Full Payment'
    });
}
```

### 3. Payment Initiation

Use the correct amount based on payment type:

```javascript
const amountToPay = bookingDetails.isPartialPayment 
    ? bookingDetails.onlinePaymentAmount 
    : bookingDetails.totalFare;

const paymentRequest = {
    bookingId: bookingDetails.bookingId,
    paymentOption: 2, // PhonePe
    amountToPay: amountToPay,
    callbackUrl: "https://cabyaari.com/#/anonymous-payment-callback",
    clientInfo: {
        userAgent: navigator.userAgent,
        ipAddress: userIpAddress,
        deviceFingerprint: deviceFingerprint
    }
};
```

### 4. Payment Success Handling

After successful payment, show appropriate message:

```javascript
const verificationResponse = response.data;

if (verificationResponse.partialPaymentAmount) {
    showSuccessMessage({
        message: 'Partial payment completed successfully!',
        onlineAmount: verificationResponse.partialPaymentAmount,
        driverAmount: verificationResponse.remainingAmountForDriver,
        receiptNumber: verificationResponse.receiptNumber
    });
} else {
    showSuccessMessage({
        message: 'Payment completed successfully!',
        totalAmount: verificationResponse.amount,
        receiptNumber: verificationResponse.receiptNumber
    });
}
```

### 5. UI Components to Update

1. **Booking Details Page**
   - Add payment type indicator
   - Show payment breakdown for partial payments
   - Display driver payment amount prominently

2. **Payment Button**
   - Update button text based on payment type
   - "Pay ₹14,360 Online" for partial payments
   - "Pay ₹60,312" for full payments

3. **Payment Success Page**
   - Show payment breakdown
   - Include driver payment instructions for partial payments
   - Display receipt number

4. **Payment Summary Component**
   ```javascript
   const PaymentSummary = ({ bookingDetails }) => {
     return (
       <div className="payment-summary">
         <h3>Payment Details</h3>
         <div className="total-fare">
           <span>Total Fare: ₹{bookingDetails.totalFare}</span>
         </div>
         
         {bookingDetails.isPartialPayment ? (
           <div className="partial-payment-breakdown">
             <div className="online-payment">
               <span>Online Payment: ₹{bookingDetails.onlinePaymentAmount}</span>
             </div>
             <div className="driver-payment">
               <span>Pay to Driver: ₹{bookingDetails.driverPaymentAmount}</span>
             </div>
           </div>
         ) : (
           <div className="full-payment">
             <span>Pay Online: ₹{bookingDetails.totalFare}</span>
           </div>
         )}
       </div>
     );
   };
   ```

## Testing

Test with the provided booking data:
- Booking ID: CY-260725-75274
- Payment Type: PARTIAL
- Online Amount: ₹14,360
- Driver Amount: ₹45,952
- Total Fare: ₹60,312

## Notes

1. The backend now properly validates payment amounts based on payment type
2. Partial payment bookings will only accept the partial amount, not the full fare
3. All existing full payment bookings will continue to work as before
4. The `paymentType` field defaults to "FULL" for backward compatibility
