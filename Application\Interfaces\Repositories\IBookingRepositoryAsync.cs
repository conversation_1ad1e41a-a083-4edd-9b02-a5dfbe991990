﻿using Application.DTOs.Booking;
using Application.DTOs.Payment;
using Domain.Entities;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Application.Interfaces.Repositories
{
   public interface IBookingRepositoryAsync : IGenericsRepositoryAsync<RLT_BOOKING>
    {
        public Task<BookingResponse> AddNewAsync(RLT_BOOKING entity);
        public Task<RazorPaymentResponse> RazorPayPaymentSuccess(string paymentId, string razorpayOrderId, string razorpaySignature);
        public Task<UserBookingsResponse> GetUserBookingsByUserIdAsync(string userId, string cursor, int pageSize);

        // Anonymous Payment Methods for Admin Bookings
        public Task<RLT_BOOKING> GetAnonymousBookingByIdAsync(string bookingId);
        public Task<bool> IsAdminBookingAsync(string bookingId);
        public Task<AnonymousPaymentVerifyResponse> ProcessAnonymousPaymentAsync(string merchantTransactionId, string orderId, string bookingId, string paymentId, string paymentType);
        public Task<string> GenerateReceiptNumberAsync(string bookingId);
        public Task<CarCategoryDetails> GetCarCategoryDetailsAsync(string categoryName);
    }
}
