﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Application.DTOs.Booking
{
  public  class BookingResponse
    {
       public string BookingId { get; set; }
        public string OrderId { get; set; }
    }

    public class UserBookingItem
    {
        public string BookingId { get; set; }
        public string PickUpCity { get; set; }
        public string DropOffCity { get; set; }
        public string TripType { get; set; }
        public string CarCategory { get; set; }
        public decimal? Fare { get; set; }
        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }
        public DateTime? PickUpDate { get; set; }
        public string PickUpTime { get; set; }
        public string TravelerName { get; set; }
        public string PhoneNumber { get; set; }
        public string RazorpayStatus { get; set; }
        public DateTime? BookingDate { get; set; }
        public bool IsPartialPayment { get; set; }
        public decimal? PaidAmount { get; set; }
    }

    public class UserBookingsResponse
    {
        public List<UserBookingItem> Bookings { get; set; } = new List<UserBookingItem>();
        public string NextCursor { get; set; }
        public bool HasMoreResult { get; set; }  // Changed to match your preference
        public int TotalCount { get; set; }
    }
}
