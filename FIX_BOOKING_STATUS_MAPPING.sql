-- =============================================
-- Fix Booking Status Mapping
-- Description: Updates existing booking records to use correct status IDs
-- that match the RLT_BOOKING_STATUS table
-- =============================================

USE [CabYaari]
GO

PRINT '=== BOOKING STATUS MAPPING FIX ==='
PRINT 'Current RLT_BOOKING_STATUS table mapping:'
PRINT 'PKID 1 = "Failed"'
PRINT 'PKID 2 = "Successful"' 
PRINT 'PKID 3 = "Payment Pending"'
PRINT ''

-- First, let's see the current distribution of Booking_Status_Id values
PRINT 'Current distribution of Booking_Status_Id values:'
SELECT 
    Booking_Status_Id,
    COUNT(*) as Count,
    CASE 
        WHEN Booking_Status_Id = 1 THEN 'Should be Failed'
        WHEN Booking_Status_Id = 2 THEN 'Should be Successful'
        WHEN Booking_Status_Id = 3 THEN 'Should be Payment Pending'
        WHEN Booking_Status_Id = 6 THEN 'INCORRECT - Currently set to 6 (should be 1 for Failed)'
        ELSE 'Unknown Status'
    END as Status_Meaning
FROM [dbo].[RLT_BOOKING]
WHERE Booking_Status_Id IS NOT NULL
GROUP BY Booking_Status_Id
ORDER BY Booking_Status_Id

PRINT ''
PRINT 'Analyzing payment status correlation:'
SELECT 
    razorpay_status,
    Booking_Status_Id,
    COUNT(*) as Count
FROM [dbo].[RLT_BOOKING]
WHERE razorpay_status IS NOT NULL
GROUP BY razorpay_status, Booking_Status_Id
ORDER BY razorpay_status, Booking_Status_Id

PRINT ''
PRINT 'Starting fix for incorrect status mappings...'

BEGIN TRANSACTION

-- Fix records that have Booking_Status_Id = 6 (should be 1 for Failed)
-- These are likely failed payments that were incorrectly set to 6
UPDATE [dbo].[RLT_BOOKING]
SET Booking_Status_Id = 1  -- Failed status
WHERE Booking_Status_Id = 6
AND (razorpay_status = 'Failed' OR razorpay_status IS NULL OR razorpay_status = '')

PRINT 'Updated records with Booking_Status_Id = 6 to 1 (Failed)'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

-- Fix any other incorrect mappings based on payment status
-- Set successful payments to status 2
UPDATE [dbo].[RLT_BOOKING]
SET Booking_Status_Id = 2  -- Successful status
WHERE razorpay_status IN ('Paid', 'COMPLETED', 'Success')
AND Booking_Status_Id != 2

PRINT 'Updated successful payments to status 2 (Successful)'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

-- Set failed payments to status 1
UPDATE [dbo].[RLT_BOOKING]
SET Booking_Status_Id = 1  -- Failed status
WHERE razorpay_status IN ('Failed', 'FAILED', 'CANCELLED')
AND Booking_Status_Id != 1

PRINT 'Updated failed payments to status 1 (Failed)'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

-- Set pending payments to status 3
UPDATE [dbo].[RLT_BOOKING]
SET Booking_Status_Id = 3  -- Payment Pending status
WHERE (razorpay_status IN ('Pending', 'PENDING', 'Created', 'CREATED') OR razorpay_status IS NULL)
AND Booking_Status_Id NOT IN (1, 2)  -- Don't change already processed payments

PRINT 'Updated pending payments to status 3 (Payment Pending)'
PRINT 'Affected rows: ' + CAST(@@ROWCOUNT AS VARCHAR(10))

COMMIT TRANSACTION

PRINT ''
PRINT 'Fix completed successfully!'
PRINT ''
PRINT 'Updated distribution of Booking_Status_Id values:'
SELECT 
    Booking_Status_Id,
    COUNT(*) as Count,
    bs.BOOKING_STATUS as Status_Description
FROM [dbo].[RLT_BOOKING] b
LEFT JOIN [dbo].[RLT_BOOKING_STATUS] bs ON b.Booking_Status_Id = bs.PKID
WHERE b.Booking_Status_Id IS NOT NULL
GROUP BY b.Booking_Status_Id, bs.BOOKING_STATUS
ORDER BY b.Booking_Status_Id

PRINT ''
PRINT 'Payment status correlation after fix:'
SELECT 
    razorpay_status,
    Booking_Status_Id,
    bs.BOOKING_STATUS as Status_Description,
    COUNT(*) as Count
FROM [dbo].[RLT_BOOKING] b
LEFT JOIN [dbo].[RLT_BOOKING_STATUS] bs ON b.Booking_Status_Id = bs.PKID
WHERE razorpay_status IS NOT NULL
GROUP BY razorpay_status, Booking_Status_Id, bs.BOOKING_STATUS
ORDER BY razorpay_status, Booking_Status_Id

PRINT ''
PRINT '=== BOOKING STATUS MAPPING FIX COMPLETED ==='
PRINT 'All stored procedures have been updated to use correct status mappings:'
PRINT '- Failed payments: Booking_Status_Id = 1'
PRINT '- Successful payments: Booking_Status_Id = 2' 
PRINT '- Pending payments: Booking_Status_Id = 3'
