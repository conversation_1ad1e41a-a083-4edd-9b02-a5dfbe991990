-- =============================================
-- Stored Procedure for Anonymous Payment Processing
-- =============================================

USE [CabYaari]
GO

-- Drop the procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_AnonymousPayment_Process]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[usp_AnonymousPayment_Process]
GO

-- Create the stored procedure
CREATE PROCEDURE [dbo].[usp_AnonymousPayment_Process]
    @BookingId nvarchar(30),
    @MerchantTransactionId nvarchar(50),
    @OrderId nvarchar(50),
    @PaymentId nvarchar(50),
    @PaymentType nvarchar(20),
    @PaymentStatus nvarchar(20),
    @PaymentDate datetime
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    DECLARE @errorMessage nvarchar(max),
           @BookingStatusId int = 2, -- Confirmed status
           @BookingPKID int,
           @CurrentFare decimal(18,2),
           @ReceiptNumber nvarchar(20),
           @OriginalPaymentType nvarchar(10),
           @OriginalPartialPaymentAmount decimal(18,2),
           @OriginalRemainingAmountForDriver decimal(18,2)

    -- Validate that the booking exists and is an admin booking (handle both PKID and Booking_Id)
    IF NOT EXISTS (SELECT 1 FROM RLT_BOOKING WHERE (Booking_Id = @BookingId OR CAST(PKID AS NVARCHAR) = @BookingId) AND IsAdminBooked = 1)
    BEGIN
        RAISERROR('Booking not found or not an admin booking', 16, 1)
        RETURN
    END

    -- Get booking details including original payment type (handle both PKID and Booking_Id)
    SELECT
        @BookingPKID = PKID,
        @CurrentFare = Fare,
        @OriginalPaymentType = ISNULL(PaymentType, 'FULL'),
        @OriginalPartialPaymentAmount = PartialPaymentAmount,
        @OriginalRemainingAmountForDriver = ISNULL(RemainingAmountForDriver, 0.00)
    FROM RLT_BOOKING
    WHERE Booking_Id = @BookingId OR CAST(PKID AS NVARCHAR) = @BookingId

    -- Generate receipt number
    SET @ReceiptNumber = 'RCPT_ANON_' + RIGHT('000000' + CAST(@BookingPKID AS VARCHAR(6)), 6)

    BEGIN TRANSACTION
    BEGIN TRY
        -- Update the booking record with payment information (preserve original payment type)
        UPDATE RLT_BOOKING
        SET
            razorpay_payment_id = @PaymentId,
            razorpay_order_id = @OrderId,
            razorpay_signature = @MerchantTransactionId, -- Store merchant transaction ID in signature field
            razorpay_status = 'Paid',
            Booking_Status_Id = @BookingStatusId,
            -- Preserve original payment type and amounts - DO NOT overwrite
            -- PaymentType = @OriginalPaymentType, -- Keep original payment type
            -- PartialPaymentAmount = @OriginalPartialPaymentAmount, -- Keep original partial amount
            -- RemainingAmountForDriver = @OriginalRemainingAmountForDriver, -- Keep original remaining amount
            Mode_Of_Payment_Id = 2, -- PhonePe payment mode
            PaymentDate = @PaymentDate,
            Updated_Date = GETDATE()
        WHERE
            Booking_Id = @BookingId OR CAST(PKID AS NVARCHAR) = @BookingId

        -- Return the payment verification response with original payment type data
        SELECT
            @PaymentStatus as PaymentStatus,
            @MerchantTransactionId as TransactionId,
            @OrderId as OrderId,
            @BookingId as BookingId,
            @CurrentFare as Amount,
            @PaymentId as PaymentId,
            @PaymentType as PaymentType,
            @PaymentDate as PaymentDate,
            @OriginalPartialPaymentAmount as PartialPaymentAmount,
            @OriginalRemainingAmountForDriver as RemainingAmountForDriver,
            @ReceiptNumber as ReceiptNumber

        COMMIT TRANSACTION
        
        SET @errorMessage = CONVERT(nvarchar(max), GETDATE(), 21) + ' Anonymous payment processed successfully for booking: ' + @BookingId
        RAISERROR(@errorMessage, 0, 1) WITH NOWAIT

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        
        SET @errorMessage = 'Error processing anonymous payment: ' + ERROR_MESSAGE()
        RAISERROR(@errorMessage, 16, 1)
        RETURN
    END CATCH
END
GO

-- Grant execute permissions (adjust as needed for your security model)
-- GRANT EXECUTE ON [dbo].[usp_AnonymousPayment_Process] TO [your_application_user]
-- GO

PRINT 'Anonymous payment stored procedure created successfully'
