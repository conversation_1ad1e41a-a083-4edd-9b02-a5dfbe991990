﻿using Application.DTOs.Account;
using Application.Wrappers;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Application.Interfaces
{
    public interface IAccountService
    {
        Task<Response<AuthenticationResponse>> AuthenticateAsync(AuthenticationRequest request, string ipAddress);
       // Task<Response<PhoneOTPSendResponse>> SendPhoneOTPAsync(PhoneOTPSendRequest request, string ipAddress);

        Task<Response<string>> RegisterAsync(RegisterRequest request, string origin);
        Task<Response<string>> ConfirmEmailAsync(string userId, string code);
        //Task<Response<string>> ConfirmPhoneAsync(ValidatePhoneOTPRequest request);
        Task ForgotPassword(ForgotPasswordRequest model, string origin);
        Task<Response<string>> ResetPassword(ResetPasswordRequest model);

        // New OTP-based authentication methods
        Task<Response<PhoneRegisterResponse>> RegisterWithPhoneAsync(PhoneRegisterRequest request, string ipAddress);
        Task<Response<AuthenticationResponse>> VerifyOtpAndLoginAsync(VerifyOtpRequest request, string ipAddress);
        Task<Response<PhoneLoginResponse>> LoginWithPhoneAsync(PhoneLoginRequest request, string ipAddress);
    }
}
