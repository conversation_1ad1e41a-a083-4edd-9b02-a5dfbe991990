using System;
using System.Collections.Generic;

namespace Application.DTOs.Sms
{
    public class SmsTemplate
    {
        public string TemplateId { get; set; }
        public string Content { get; set; }
        public SmsTemplateType Type { get; set; }
        public List<string> PlaceholderKeys { get; set; } = new List<string>();
    }

    public enum SmsTemplateType
    {
        RegistrationOtp,
        LoginOtp,
        BookingConfirmation,
        AdminBookingAlert
    }

    public class SmsTemplateRequest
    {
        public string PhoneNumber { get; set; }
        public SmsTemplateType TemplateType { get; set; }
        public Dictionary<string, string> PlaceholderValues { get; set; } = new Dictionary<string, string>();
    }

    public class BookingConfirmationSmsData
    {
        public string FromLocation { get; set; }
        public string ToLocation { get; set; }
        public string BookingDate { get; set; }
        public string BookingTime { get; set; }
        public string PickupDate { get; set; }
        public string PickupTime { get; set; }
        public string CabType { get; set; }
        public string PaymentDetails { get; set; }
        public string PhoneNumber { get; set; }
    }

    public class AdminBookingAlertSmsData
    {
        public string CustomerName { get; set; }
        public string FromLocation { get; set; }
        public string ToLocation { get; set; }
        public string TotalFare { get; set; }
        public string PaidAmount { get; set; }
        public string BookingId { get; set; }
        public string AdminPhoneNumber { get; set; }
    }
}
