namespace Domain.Settings
{
    public class FareSettings
    {
        /// <summary>
        /// Additional distance buffer in KM to compensate for detours and route variations
        /// </summary>
        public int DetourBufferKM { get; set; } = 20;

        /// <summary>
        /// Speed threshold in km/h below which time compensation is applied
        /// </summary>
        public int TimeCompensationSpeedThresholdKmh { get; set; } = 50;

        /// <summary>
        /// Standard speed in km/h used for time compensation calculations
        /// </summary>
        public int TimeCompensationStandardSpeedKmh { get; set; } = 60;

        /// <summary>
        /// Time buffer in minutes added to journey time for compensation calculations
        /// </summary>
        public int TimeBufferMinutes { get; set; } = 5;

        /// <summary>
        /// Distance threshold in KM below which short distance rate adjustment is applied
        /// </summary>
        public int ShortDistanceThresholdKM { get; set; } = 200;

        /// <summary>
        /// Rate adjustment amount (in ₹/KM) for short distance trips
        /// </summary>
        public decimal ShortDistanceRateAdjustment { get; set; } = 1.0m;
    }
}
