# PhonePe API Endpoint Fix for Anonymous Payments

## Issue Identified
The anonymous payment system was failing with the error:
```json
{
    "Succeeded": false,
    "Message": "Failed to initiate payment: PhonePe payment initiation failed: {\"message\":\"Bad Request - Api Mapping Not Found\"}",
    "Errors": null,
    "Data": null
}
```

## Root Cause
The anonymous payment implementation was using incorrect PhonePe API endpoints that don't exist:
- **Wrong Initiate Endpoint**: `/v1/payment/initiate`
- **Wrong Verify Endpoint**: `/v1/payment/status/{transactionId}`

## Solution Applied

### 1. **Fixed Payment Initiation Endpoint**
**File**: `Application/Features/Payments/AnonymousPayments/InitiateAnonymousPayment.cs`

**Before (Incorrect)**:
```csharp
var response = await client.PostAsync($"{apiUrl}/v1/payment/initiate", content);
```

**After (Fixed)**:
```csharp
var response = await client.PostAsync($"{apiUrl}/checkout/v2/pay", content);
```

### 2. **Fixed Payment Verification Endpoint**
**File**: `Application/Features/Payments/AnonymousPayments/VerifyAnonymousPayment.cs`

**Before (Incorrect)**:
```csharp
var verifyUrl = $"{apiUrl}/v1/payment/status/{request.Request.MerchantTransactionId}";
var response = await client.GetAsync(verifyUrl);
var phonePeVerifyResponse = JsonSerializer.Deserialize<PhonePeVerifyResponse>(responseContent);
```

**After (Fixed)**:
```csharp
var statusUrl = $"{apiUrl}/checkout/v2/order/{request.Request.MerchantTransactionId}/status";
var response = await client.GetAsync(statusUrl);
var phonePeOrderResponse = JsonSerializer.Deserialize<PhonePeOrderStatusResponse>(responseContent);
```

### 3. **Updated Response Handling**
**Changes Made**:
- Added proper import for `PhonePeOrderStatusResponse`
- Updated response parsing to match the actual PhonePe API response structure
- Fixed payment detail extraction from the response
- Aligned with the working `VerifyPhonePePayment` implementation

## Key Changes Summary

### InitiateAnonymousPayment.cs
✅ **Endpoint**: Changed to `/checkout/v2/pay` (matches working implementation)
✅ **Request Structure**: Uses existing `PhonePePaymentInitiateRequest` 
✅ **Response Handling**: Uses existing `PhonePePaymentInitiateResponse`
✅ **Error Handling**: Improved logging and error messages

### VerifyAnonymousPayment.cs
✅ **Endpoint**: Changed to `/checkout/v2/order/{transactionId}/status`
✅ **Response Type**: Changed to `PhonePeOrderStatusResponse`
✅ **Payment Detail Extraction**: Properly extracts transaction details
✅ **Status Validation**: Uses same logic as working verification

## Verified Working Endpoints

Based on the existing working implementation:

### Payment Initiation
- **Endpoint**: `POST {apiUrl}/checkout/v2/pay`
- **Request**: `PhonePePaymentInitiateRequest`
- **Response**: `PhonePePaymentInitiateResponse`

### Payment Verification  
- **Endpoint**: `GET {apiUrl}/checkout/v2/order/{merchantTransactionId}/status`
- **Response**: `PhonePeOrderStatusResponse`

## Testing the Fix

### 1. **Payment Initiation Test**
```bash
# Should now return a valid payment URL instead of "Api Mapping Not Found"
POST /api/v1/anonymous-payments/initiate
```

### 2. **Payment Verification Test**
```bash
# Should now properly verify payment status
POST /api/v1/anonymous-payments/verify
```

## Expected Behavior After Fix

### Successful Payment Initiation:
```json
{
    "succeeded": true,
    "message": "Success",
    "data": {
        "tokenUrl": "https://mercury.phonepe.com/transact/...",
        "merchantTransactionId": "ANON_TXN_1705234567890",
        "orderId": "ORDER_ANON_1705234567890",
        "bookingId": "encrypted_booking_id",
        "expiresAt": "2024-01-15T11:00:00Z",
        "amount": 2950
    }
}
```

### Successful Payment Verification:
```json
{
    "succeeded": true,
    "message": "Success",
    "data": {
        "paymentStatus": "COMPLETED",
        "transactionId": "ANON_TXN_1705234567890",
        "orderId": "ORDER_ANON_1705234567890",
        "bookingId": "encrypted_booking_id",
        "amount": 2950,
        "paymentId": "extracted_from_phonepe_response",
        "paymentType": "PhonePe",
        "paymentDate": "2024-01-15T10:45:00Z",
        "receiptNumber": "RCPT_ANON_001234"
    }
}
```

## Files Updated
- `Application/Features/Payments/AnonymousPayments/InitiateAnonymousPayment.cs`
- `Application/Features/Payments/AnonymousPayments/VerifyAnonymousPayment.cs`

The anonymous payment system now uses the same proven PhonePe API endpoints and response handling as the working regular payment system.
