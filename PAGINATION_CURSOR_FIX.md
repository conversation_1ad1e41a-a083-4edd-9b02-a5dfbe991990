# Pagination Cursor Fix for User Bookings API

## Problem Description

The user bookings API (`/api/v1/booking/user-bookings`) was returning inconsistent pagination data:
- `nextCursor` was `null` 
- `hasMoreResult` was `true`

This created an inconsistent state where the API indicated more results were available but didn't provide a cursor to fetch them.

## Root Cause Analysis

**Primary Issue**: Username to GUID mapping problem
- The API receives `username` (e.g., "kunalsaini9") from JWT token
- But bookings are stored with `user GUID` (e.g., "63a3af25-2ae4-4d14-a0fd-c7d28d3d558f") in `Booking_Created_By` field
- The stored procedure was trying to match username directly, finding no results

**Secondary Issue**: Cursor generation logic
```csharp
// Set next cursor to the last item's booking date
if (response.HasMoreResult && bookingList.Any())
{
    response.NextCursor = bookingList.Last().BookingDate?.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
}
```

The `BookingDate` field (mapped from `b.Created_Date` in the database) was `null` for all bookings, causing the `NextCursor` to always be `null` even when more results existed.

**Debug Results**:
- 19 bookings found by `Booking_Created_By` GUID ✅
- 0 bookings found by username ❌
- User exists with both username and GUID ✅

## Solution Implemented

### 1. Username to GUID Mapping Fix (`usp_Booking_GetUserBookings_V3.sql`)

The core fix resolves the user identification issue:

```sql
-- Get the user's GUID from their username
DECLARE @UserGuid NVARCHAR(450) = NULL;

SELECT @UserGuid = Id
FROM [Identity].[User]
WHERE UserName = @UserId;

-- Then use the GUID to find bookings
WHERE b.Booking_Created_By = @UserGuid
```

### 2. Repository Layer Fix (`BookingRepositoryAsync.cs`)

Updated the cursor generation logic to use a fallback approach:

```csharp
// Set next cursor - use BookingId as cursor since BookingDate is null
if (response.HasMoreResult && bookingList.Any())
{
    var lastBooking = bookingList.Last();
    // If BookingDate is available, use it; otherwise use BookingId
    if (lastBooking.BookingDate.HasValue)
    {
        response.NextCursor = lastBooking.BookingDate.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
    }
    else
    {
        // Use BookingId as fallback cursor
        response.NextCursor = lastBooking.BookingId;
    }
}
```

### 2. Stored Procedure Fix (`usp_Booking_GetUserBookings.sql`)

Enhanced the stored procedure to handle both date-based and BookingId-based cursors:

#### Cursor Parsing
```sql
-- Parse cursor - can be either a date or a BookingId
DECLARE @CursorDate DATETIME = NULL;
DECLARE @CursorBookingId NVARCHAR(50) = NULL;

IF @Cursor IS NOT NULL AND @Cursor != ''
BEGIN
    -- Try to parse as date first
    BEGIN TRY
        SET @CursorDate = CAST(@Cursor AS DATETIME);
    END TRY
    BEGIN CATCH
        -- If not a date, treat as BookingId
        SET @CursorBookingId = @Cursor;
    END CATCH
END
```

#### WHERE Clause Enhancement
```sql
WHERE u.UserName = @UserId
AND (
    -- Date-based cursor (preferred when BookingDate is available)
    (@CursorDate IS NOT NULL AND b.Created_Date < @CursorDate)
    -- BookingId-based cursor (fallback when BookingDate is null)
    OR (@CursorBookingId IS NOT NULL AND b.Booking_Id < @CursorBookingId)
    -- No cursor (first page)
    OR (@CursorDate IS NULL AND @CursorBookingId IS NULL)
)
```

#### Improved Ordering
```sql
ORDER BY 
    CASE WHEN BookingDate IS NOT NULL THEN BookingDate ELSE '1900-01-01' END DESC,
    BookingId DESC;
```

## Benefits

1. **Consistent Pagination**: The API now always provides a valid cursor when more results are available
2. **Backward Compatibility**: Still supports date-based cursors when `BookingDate` is available
3. **Fallback Mechanism**: Uses `BookingId` as cursor when `BookingDate` is null
4. **Reliable Ordering**: Ensures consistent result ordering regardless of cursor type

## Testing

To test the fix:

1. **First Page Request**:
   ```
   GET /api/v1/booking/user-bookings?pageSize=10
   ```

2. **Subsequent Page with BookingId Cursor**:
   ```
   GET /api/v1/booking/user-bookings?pageSize=10&cursor=CY-190725-40282
   ```

3. **Subsequent Page with Date Cursor** (if BookingDate becomes available):
   ```
   GET /api/v1/booking/user-bookings?pageSize=10&cursor=2025-07-06T10:30:00.000Z
   ```

## Files Modified

1. `Infrastructure.Persistence/Repositories/BookingRepositoryAsync.cs`
2. `StoredProcedures/Booking/usp_Booking_GetUserBookings.sql`

## Next Steps

1. Deploy the updated stored procedure to the database
2. Deploy the updated repository code
3. Test the pagination functionality
4. Consider fixing the root cause of null `Created_Date` values in the database for future bookings
