# PhonePe Payment Mode Mapping Implementation (Database-Driven Approach)

## Overview
This implementation adds support for automatically setting the `Mode_Of_Payment_Id` field in the `RLT_BOOKING` table based on the payment method used in PhonePe transactions. The solution uses a **database-driven approach** where payment method mappings are stored in the database rather than hardcoded in the application.

## Problem Statement
The `Mode_Of_Payment_Id` field in the `RLT_BOOKING` table was null for PhonePe payments because the system wasn't extracting and mapping the payment method from PhonePe's response to the corresponding payment method ID in the `RLT_PAYMENT_METHOD` table.

## Solution Approach
Instead of hardcoding payment method mappings in C# code, we:
1. **Added a new column** `PhonePePaymentMethod` to the `RLT_PAYMENT_METHOD` table
2. **Store PhonePe payment method strings** in this column for each payment method
3. **Pass the PhonePe payment method string** to the stored procedure
4. **Let the stored procedure perform the lookup** to find the matching `PKID`

This approach is more maintainable because:
- ✅ No hardcoded mappings in application code
- ✅ Easy to add new payment methods by updating the database
- ✅ Changes don't require code deployment
- ✅ Centralized payment method management

## PhonePe Response Analysis
From the provided PhonePe verify response:
```json
{
  "paymentDetails": [
    {
      "paymentMode": "CARD",
      "transactionId": "OM2507191244501182375267",
      "state": "COMPLETED",
      "splitInstruments": [
        {
          "instrument": {
            "type": "CREDIT_CARD",
            "bankId": "SBIN"
          }
        }
      ]
    }
  ]
}
```

## Database Schema Changes

### New Column: RLT_PAYMENT_METHOD.PhonePePaymentMethod
**✅ Based on Official PhonePe API Documentation**

| PKID | Payment_Method_Name | PhonePePaymentMethod | Description | Official Source |
|------|-------------------|---------------------|-------------|-----------------|
| 1    | Cash              | CASH                | Cash payments (not used in PhonePe) | N/A |
| 2    | Credit Card       | CREDIT_CARD         | Credit card payments | ✅ `instrument.type` |
| 3    | Debit Card        | DEBIT_CARD          | Debit card payments | ✅ `instrument.type` |
| 4    | Online Payment    | CARD                | Generic card fallback | ✅ `paymentMode` |
| 5    | UPI               | UPI                 | UPI payments | ✅ `rail.type` |
| 6    | Net Banking       | NET_BANKING         | Net banking payments | ✅ `instrument.type` |
| 7    | Wallet            | WALLET              | Digital wallet payments | ✅ `instrument.type` |
| 8    | Bank Transfer     | ACCOUNT             | UPI/Bank account payments | ✅ `instrument.type` |

### Lookup Logic in Stored Procedure
1. **Exact Match**: Look for exact match of PhonePe payment method
2. **Fallback for Cards**: If `CREDIT_CARD` or `DEBIT_CARD` not found, fallback to `CARD`
3. **Fallback for UPI**: If `ACCOUNT` not found, fallback to `UPI`
4. **Default**: If no match found, default to `PKID = 4` (Online Payment)

### Official PhonePe API Reference
**Source**: [PhonePe Order Status API Documentation](https://developer.phonepe.com/v1/reference/ios-order-status-standard-checkout)

**Confirmed Payment Method Types**:
- ✅ `paymentDetails.instrument.type`: `[ACCOUNT, CREDIT_CARD, DEBIT_CARD, NET_BANKING, WALLET]`
- ✅ `paymentDetails.paymentMode`: `[UPI_INTENT, UPI_COLLECT, UPI_QR, CARD, TOKEN, NET_BANKING]`
- ✅ `paymentDetails.rail.type`: `[UPI, PG, PPI_WALLET]`

## Implementation Changes

### 1. Updated VerifyPhonePePayment.cs
**File:** `Application/Features/Payments/PhonePe/VerifyPhonePePayment.cs`

#### Enhanced Payment Method Extraction
```csharp
// Extract transaction details if payment is completed
string phonePePaymentMethod = null;
if (paymentStatus == "Paid" && phonePeResponse.paymentDetails != null && phonePeResponse.paymentDetails.Count > 0)
{
    var latestPayment = phonePeResponse.paymentDetails[0];
    transactionId = latestPayment.transactionId;
    paymentId = latestPayment.transactionId;

    // Get the detailed payment method from splitInstruments if available
    if (latestPayment.splitInstruments != null && latestPayment.splitInstruments.Count > 0)
    {
        var instrument = latestPayment.splitInstruments[0].instrument;
        phonePePaymentMethod = instrument?.type; // This gives us "CREDIT_CARD", "DEBIT_CARD", "UPI", etc.
    }

    // Fallback to paymentMode if instrument type is not available
    if (string.IsNullOrEmpty(phonePePaymentMethod))
    {
        phonePePaymentMethod = latestPayment.paymentMode; // This gives us "CARD", "UPI", etc.
    }
}

// Store the PhonePe payment method string - the stored procedure will handle the lookup
booking.PhonePePaymentMethod = phonePePaymentMethod;
```

#### Key Changes:
- ✅ **Removed hardcoded mapping logic** from C# code
- ✅ **Extract detailed payment method** from `splitInstruments[0].instrument.type` (e.g., "CREDIT_CARD")
- ✅ **Fallback to paymentMode** if detailed type not available (e.g., "CARD")
- ✅ **Pass string to stored procedure** instead of mapped integer

### 2. Updated Stored Procedure
**File:** `STORED_PROCEDURES_V2.sql` and `DATABASE_SCHEMA_PHONEPE_PAYMENT_METHOD.sql`

#### Added Parameter
```sql
@PhonePePaymentMethod nvarchar(50) NULL
```

#### Added Lookup Logic
```sql
-- Lookup Mode_Of_Payment_Id based on PhonePe payment method
IF @PhonePePaymentMethod IS NOT NULL
BEGIN
    SELECT TOP 1 @ModeOfPaymentId = PKID
    FROM [dbo].[RLT_PAYMENT_METHOD]
    WHERE PhonePePaymentMethod = @PhonePePaymentMethod
    AND Is_Active = 1

    -- If no exact match found, try to find a fallback
    IF @ModeOfPaymentId IS NULL
    BEGIN
        -- For CREDIT_CARD or DEBIT_CARD, fallback to generic CARD
        IF @PhonePePaymentMethod IN ('CREDIT_CARD', 'DEBIT_CARD')
        BEGIN
            SELECT TOP 1 @ModeOfPaymentId = PKID
            FROM [dbo].[RLT_PAYMENT_METHOD]
            WHERE PhonePePaymentMethod = 'CARD'
            AND Is_Active = 1
        END

        -- If still no match, default to Online Payment (PKID = 4)
        IF @ModeOfPaymentId IS NULL
        BEGIN
            SET @ModeOfPaymentId = 4
        END
    END
END
```

#### Updated UPDATE Statement
```sql
UPDATE RLT_BOOKING
SET
    -- ... existing fields ...
    -- Update Mode_Of_Payment_Id based on PhonePe payment method lookup
    Mode_Of_Payment_Id = ISNULL(@ModeOfPaymentId, Mode_Of_Payment_Id),
    Updated_Date = GETDATE()
WHERE
    Booking_Id = @BookingId
```

### 3. Updated Repository
**File:** `Infrastructure.Persistence/Repositories/BookingRepositoryAsync.cs`

#### Updated Parameter in UpdateAsync Method
```csharp
_params.Add("@PhonePePaymentMethod", entity.PhonePePaymentMethod, DbType.String);
```

### 4. Updated Domain Entity
**File:** `Domain/Entities/RLT_BOOKING.cs`

#### Added New Property
```csharp
public string PhonePePaymentMethod { get; set; } // PhonePe payment method string for lookup
```

## Expected Behavior

### For the Provided Example:
Based on your PhonePe response:
```json
{
  "paymentDetails": [
    {
      "paymentMode": "CARD",
      "splitInstruments": [
        {
          "instrument": {
            "type": "CREDIT_CARD"
          }
        }
      ]
    }
  ]
}
```

**Processing Flow:**
1. **Extract**: `instrument.type = "CREDIT_CARD"`
2. **Database Lookup**: Find `PKID` where `PhonePePaymentMethod = "CREDIT_CARD"`
3. **Result**: `Mode_Of_Payment_Id = 2` (Credit Card)

### For Other Payment Methods:
- **UPI Payment**: `"type": "UPI"` → Database lookup → `Mode_Of_Payment_Id = 5`
- **Debit Card**: `"type": "DEBIT_CARD"` → Database lookup → `Mode_Of_Payment_Id = 3`
- **Net Banking**: `"type": "NETBANKING"` → Database lookup → `Mode_Of_Payment_Id = 6`
- **Wallet**: `"type": "WALLET"` → Database lookup → `Mode_Of_Payment_Id = 7`
- **Unknown**: Any unmapped type → Fallback → `Mode_Of_Payment_Id = 4` (Online Payment)

## Database Migration
Run the `DATABASE_SCHEMA_PHONEPE_PAYMENT_METHOD.sql` script to:
1. Add `PhonePePaymentMethod` column to `RLT_PAYMENT_METHOD` table
2. Populate the column with appropriate PhonePe payment method strings
3. Update the stored procedure with lookup logic

## Testing
1. Process a PhonePe payment
2. Verify the payment using the VerifyPhonePePayment endpoint
3. Check that the `Mode_Of_Payment_Id` field is populated in the `RLT_BOOKING` table
4. Verify the mapping is correct based on the payment method used

## Files Modified
1. `Application/Features/Payments/PhonePe/VerifyPhonePePayment.cs` - Enhanced payment method extraction
2. `STORED_PROCEDURES_V2.sql` - Added database lookup logic
3. `Infrastructure.Persistence/Repositories/BookingRepositoryAsync.cs` - Updated parameter passing
4. `Domain/Entities/RLT_BOOKING.cs` - Added PhonePePaymentMethod property

## Files Created
1. `DATABASE_SCHEMA_PHONEPE_PAYMENT_METHOD.sql` - Complete database migration script
2. `PHONEPE_PAYMENT_MODE_MAPPING_IMPLEMENTATION.md` - This documentation

## Advantages of This Approach

### ✅ **Maintainability**
- No hardcoded mappings in application code
- Easy to add new payment methods without code changes
- Centralized payment method management in database

### ✅ **Flexibility**
- Support for both detailed instrument types (`CREDIT_CARD`) and generic modes (`CARD`)
- Fallback logic for unmapped payment methods
- Easy to modify mappings via database updates

### ✅ **Accuracy**
- Uses detailed payment instrument information when available
- Proper distinction between credit and debit cards
- Comprehensive logging for debugging

This database-driven implementation ensures that all PhonePe payments will have the appropriate payment method recorded in the database for better tracking and reporting, while maintaining maximum flexibility and maintainability.
