# User Bookings API - Partial Payment Support Implementation

## Overview
This document outlines the implementation of partial payment support in the User Bookings API endpoint (`/api/v1/booking/user-bookings`).

## Changes Made

### 1. Updated DTO (Data Transfer Object)
**File:** `Application/DTOs/Booking/BookingResponse.cs`

**Added Fields to UserBookingItem:**
```csharp
public bool IsPartialPayment { get; set; }
public decimal? PaidAmount { get; set; }
```

### 2. Enhanced Stored Procedure
**File:** `StoredProcedures/Booking/usp_Booking_GetUserBookings_V4.sql`

**New Logic:**
- `IsPartialPayment`: Returns `1` if `PaymentType = 'PARTIAL'`, otherwise `0`
- `Fare`: For partial payments, returns `Basic_Fare + GST` (total fare); for full payments, returns existing `Fare` column
- `PaidAmount`: Returns `PartialPaymentAmount` for partial payments, `Fare` for full payments

**SQL Logic:**
```sql
-- For partial payments, fare should be Basic_Fare + GST (total fare)
-- For full payments, use the existing Fare column
CASE
    WHEN b.PaymentType = 'PARTIAL' THEN ROUND(ISNULL(b.Basic_Fare, 0) + ISNULL(b.GST, 0), 0)
    ELSE ROUND(ISNULL(b.Fare, 0), 0)
END as Fare,

-- Add partial payment fields
CASE WHEN b.PaymentType = 'PARTIAL' THEN 1 ELSE 0 END as IsPartialPayment,
CASE
    WHEN b.PaymentType = 'PARTIAL' THEN ROUND(ISNULL(b.PartialPaymentAmount, 0), 0)
    ELSE ROUND(ISNULL(b.Fare, 0), 0)
END as PaidAmount,
```

### 3. Updated Repository
**File:** `Infrastructure.Persistence/Repositories/BookingRepositoryAsync.cs`

**Change:**
- Updated stored procedure call from `usp_Booking_GetUserBookings_V3` to `usp_Booking_GetUserBookings_V4`

## API Response Format

### Before (Original)
```json
{
    "succeeded": true,
    "message": "User bookings retrieved successfully.",
    "data": {
        "bookings": [
            {
                "bookingId": "CY-300725-31086",
                "pickUpCity": "Jaipur",
                "dropOffCity": "Kolhapur",
                "tripType": "Round Trip",
                "carCategory": "SUV Premium",
                "fare": 7050.00,
                "razorpayStatus": "Paid"
            }
        ]
    }
}
```

### After (Enhanced)
```json
{
    "succeeded": true,
    "message": "User bookings retrieved successfully.",
    "data": {
        "bookings": [
            {
                "bookingId": "CY-300725-89886",
                "pickUpCity": "Jaipur",
                "dropOffCity": "Guwahati",
                "tripType": "Round Trip",
                "carCategory": "SUV Premium",
                "fare": 41874.00,
                "razorpayStatus": "Paid",
                "isPartialPayment": true,
                "paidAmount": 9970.00
            }
        ]
    }
}
```

## Database Fields Used

The implementation leverages existing database fields in the `RLT_BOOKING` table:

- `PaymentType`: "PARTIAL" or "FULL"
- `PartialPaymentAmount`: Amount paid online for partial payments
- `Fare`: Total booking fare

## Logic Summary

1. **Full Payment Bookings:**
   - `isPartialPayment`: `false`
   - `paidAmount`: Equal to `fare` (full amount paid)

2. **Partial Payment Bookings:**
   - `isPartialPayment`: `true`
   - `paidAmount`: Equal to `partialPaymentAmount` (amount paid online)
   - Remaining amount (`RemainingAmountForDriver`) is collected by driver

## Deployment Steps

1. **Execute Database Update:**
   ```sql
   -- Run the database update script
   -- File: DATABASE_UPDATE_USER_BOOKINGS_PARTIAL_PAYMENT.sql
   ```

2. **Deploy Application Code:**
   - Updated DTO with new fields
   - Updated repository to use new stored procedure
   - No controller changes required (automatic mapping)

## Testing

### Test Cases

1. **Full Payment Booking:**
   - Verify `isPartialPayment` = `false`
   - Verify `paidAmount` = `fare`

2. **Partial Payment Booking:**
   - Verify `isPartialPayment` = `true`
   - Verify `paidAmount` = `partialPaymentAmount`

### Sample Test Data
Based on your provided data:

**Booking CY-300725-89886 (Partial Payment):**
- `basicFare`: 39880.00
- `gst`: 1994.00
- `paymentType`: "PARTIAL"
- `partialPaymentAmount`: 9970.00
- Expected API response:
  - `fare`: 41874.00 (Basic_Fare + GST = 39880.00 + 1994.00)
  - `isPartialPayment`: `true`
  - `paidAmount`: 9970.00

## Benefits

1. **Clear Payment Status:** Frontend can easily identify partial vs full payments
2. **Accurate Amount Display:** Shows actual amount paid, not total fare
3. **Better UX:** Users can see exactly how much they paid online
4. **Driver Information:** Remaining amount is clear for cash collection

## Backward Compatibility

- All existing fields remain unchanged
- New fields are additive only
- Existing API consumers will continue to work
- New consumers can leverage enhanced payment information
