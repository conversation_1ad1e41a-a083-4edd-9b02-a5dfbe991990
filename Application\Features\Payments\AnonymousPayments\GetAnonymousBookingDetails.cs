using Application.DTOs.Payment;
using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using AutoMapper;
using MediatR;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading;
using System.Threading.Tasks;
using Application.Utilities;

namespace Application.Features.Payments.AnonymousPayments
{
    public class GetAnonymousBookingDetailsQuery : IRequest<Response<AnonymousBookingDetailsResponse>>
    {
        public string EncryptedBookingId { get; set; }
    }

    public class GetAnonymousBookingDetailsQueryHandler : IRequestHandler<GetAnonymousBookingDetailsQuery, Response<AnonymousBookingDetailsResponse>>
    {
        private readonly IBookingRepositoryAsync _bookingRepository;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;

        public GetAnonymousBookingDetailsQueryHandler(
            IBookingRepositoryAsync bookingRepository,
            IMapper mapper,
            IConfiguration configuration)
        {
            _bookingRepository = bookingRepository;
            _mapper = mapper;
            _configuration = configuration;
        }

        public async Task<Response<AnonymousBookingDetailsResponse>> Handle(GetAnonymousBookingDetailsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                // Decrypt the booking ID
                var encryptionPassphrase = _configuration["AnonymousPaymentSettings:EncryptionPassphrase"];
                var bookingId = QueryStringEncoding.DecryptFromUrl(request.EncryptedBookingId, encryptionPassphrase);

                Console.WriteLine($"[GetAnonymousBookingDetails] Decrypted booking ID: {bookingId}");

                // Validate that this is an admin booking
                var isAdminBooking = await _bookingRepository.IsAdminBookingAsync(bookingId);
                if (!isAdminBooking)
                {
                    throw new ApiException("This booking is not available for anonymous payment.");
                }

                // Get booking details
                var booking = await _bookingRepository.GetAnonymousBookingByIdAsync(bookingId);
                if (booking == null)
                {
                    throw new ApiException("Booking not found.");
                }

                // Get car category details from database
                var carCategoryDetails = await _bookingRepository.GetCarCategoryDetailsAsync(booking.CarCategory);

                Console.WriteLine($"[GetAnonymousBookingDetails] Car Category: {booking.CarCategory}");
                Console.WriteLine($"[GetAnonymousBookingDetails] Car Details Found: {carCategoryDetails != null}");
                if (carCategoryDetails != null)
                {
                    Console.WriteLine($"[GetAnonymousBookingDetails] Capacity: {carCategoryDetails.Capacity}, Features: {carCategoryDetails.Features}");
                }

                // Determine payment type and amounts
                var isPartialPayment = !string.IsNullOrEmpty(booking.PaymentType) && booking.PaymentType.Equals("PARTIAL", StringComparison.OrdinalIgnoreCase);
                var onlinePaymentAmount = isPartialPayment ? (booking.PartialPaymentAmount ?? 0) : (booking.Fare ?? 0);
                var driverPaymentAmount = isPartialPayment ? (booking.RemainingAmountForDriver ?? 0) : 0;

                Console.WriteLine($"[GetAnonymousBookingDetails] Payment Type: {booking.PaymentType}");
                Console.WriteLine($"[GetAnonymousBookingDetails] Is Partial Payment: {isPartialPayment}");
                Console.WriteLine($"[GetAnonymousBookingDetails] Online Payment Amount: {onlinePaymentAmount}");
                Console.WriteLine($"[GetAnonymousBookingDetails] Driver Payment Amount: {driverPaymentAmount}");

                // Map to response DTO
                var response = new AnonymousBookingDetailsResponse
                {
                    BookingId = request.EncryptedBookingId, // Return encrypted ID
                    TripType = booking.TripType,
                    PickUpCity = booking.PickUpCity,
                    DropOffCity = booking.DropOffCity,
                    PickUpAddress = booking.PickUpAddress,
                    DropOffAddress = booking.DropOffAddress,
                    PickUpDate = booking.PickUpDate?.ToString("yyyy-MM-dd"),
                    PickUpTime = booking.PickUpTime,
                    CarCategory = booking.CarCategory,
                    CarFeatures = carCategoryDetails?.Features ?? "AC, Music System, GPS Tracking", // Use DB features or fallback
                    CarCapacity = carCategoryDetails?.Capacity ?? 4, // Use DB capacity or fallback
                    Distance = booking.Distance ?? 0,
                    Duration = booking.Duration,
                    BasicFare = booking.BasicFare ?? 0,
                    Gst = booking.Gst ?? 0,
                    TotalFare = booking.Fare ?? 0,
                    PerKMCharges = booking.Distance > 0 ? ((booking.BasicFare ?? 0) / booking.Distance ?? 1).ToString("F2") : "0.00",
                    FixRateNote = $"Fixed rate for {booking.PickUpCity}-{booking.DropOffCity} route",
                    TollCharge = booking.TollCharge ?? 0,
                    TravelerName = booking.TravelerName,
                    PhoneNumber = booking.PhoneNumber,
                    MailId = booking.MailId,
                    PaymentStatus = MapPaymentStatus(booking.RazorpayStatus),
                    IsPaymentPending = booking.RazorpayStatus != "Paid",
                    CashAmountToPayDriver = booking.CashAmountToPayDriver,
                    BookingCreatedAt = booking.BookingDate ?? DateTime.UtcNow,
                    ExpiresAt = (booking.BookingDate ?? DateTime.UtcNow).AddHours(24), // 24 hours expiry

                    // Partial Payment Details
                    PaymentType = booking.PaymentType ?? "FULL",
                    PartialPaymentAmount = booking.PartialPaymentAmount,
                    RemainingAmountForDriver = booking.RemainingAmountForDriver,
                    IsPartialPayment = isPartialPayment,
                    OnlinePaymentAmount = onlinePaymentAmount,
                    DriverPaymentAmount = driverPaymentAmount
                };

                return new Response<AnonymousBookingDetailsResponse>(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[GetAnonymousBookingDetails] Error: {ex.Message}");
                throw new ApiException($"Failed to retrieve booking details: {ex.Message}");
            }
        }

        private string MapPaymentStatus(string razorpayStatus)
        {
            return razorpayStatus switch
            {
                "Paid" => "COMPLETED",
                "Pending" => "PENDING",
                "Failed" => "FAILED",
                _ => "PENDING"
            };
        }
    }
}
