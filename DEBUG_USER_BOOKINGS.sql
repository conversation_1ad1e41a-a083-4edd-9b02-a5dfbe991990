-- =============================================
-- Debug <PERSON>ript for User Bookings Issue
-- =============================================

USE [CabYaari]
GO

-- First, let's see what's in the booking table
PRINT '=== BOOKING DATA ANALYSIS ===';

SELECT TOP 5
    PKID,
    Booking_Id,
    Created_By,
    Booking_Created_By,
    [Name] as TravelerName,
    Mobile_No1,
    Mail_Id,
    Created_Date
FROM [dbo].[RLT_BOOKING]
ORDER BY PKID DESC;

PRINT '';
PRINT '=== USER DATA ANALYSIS ===';

-- Check what users exist
SELECT TOP 5
    Id,
    UserName,
    Email,
    PhoneNumber
FROM [Identity].[User]
ORDER BY Id;

PRINT '';
PRINT '=== BOOKING-USER RELATIONSHIP ANALYSIS ===';

-- Check the relationship between bookings and users
SELECT 
    b.Booking_Id,
    b.Created_By,
    b.Booking_Created_By,
    u.Id as UserId,
    u.UserName,
    u.Email
FROM [dbo].[RLT_BOOKING] b
LEFT JOIN [Identity].[User] u ON CAST(b.Created_By AS NVARCHAR) = u.Id
WHERE b.Booking_Id = 'CY-260725-17641';  -- Your specific booking

PRINT '';
PRINT '=== TEST DIFFERENT USER MATCHING APPROACHES ===';

-- Test 1: Direct Booking_Created_By match
DECLARE @TestUserId1 NVARCHAR(450) = '63a3af25-2ae4-4d14-a0fd-c7d28d3d558f';  -- From your data
DECLARE @TestUserId2 NVARCHAR(450) = 'kunalsaini9';  -- Assuming this is the username

PRINT 'Test 1: Matching by Booking_Created_By = ' + @TestUserId1;
SELECT COUNT(*) as BookingCount
FROM [dbo].[RLT_BOOKING] 
WHERE Booking_Created_By = @TestUserId1;

PRINT 'Test 2: Matching by UserName = ' + @TestUserId2;
SELECT COUNT(*) as BookingCount
FROM [dbo].[RLT_BOOKING] b
INNER JOIN [Identity].[User] u ON CAST(b.Created_By AS NVARCHAR) = u.Id
WHERE u.UserName = @TestUserId2;

PRINT 'Test 3: Check if user exists with username = ' + @TestUserId2;
SELECT COUNT(*) as UserCount
FROM [Identity].[User]
WHERE UserName = @TestUserId2;

PRINT 'Test 4: Check if user exists with Id = ' + @TestUserId1;
SELECT COUNT(*) as UserCount
FROM [Identity].[User]
WHERE Id = @TestUserId1;

PRINT '';
PRINT '=== RECOMMENDATION ===';
PRINT 'Based on the results above, we can determine the correct approach for user matching.';
