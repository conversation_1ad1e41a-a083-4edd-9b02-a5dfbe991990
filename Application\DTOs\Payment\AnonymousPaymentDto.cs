using System;
using System.ComponentModel.DataAnnotations;

namespace Application.DTOs.Payment
{
    // Request DTOs
    public class AnonymousPaymentInitiateRequest
    {
        [Required]
        public string BookingId { get; set; }
        
        [Required]
        [Range(1, 4)]
        public int PaymentOption { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue)]
        public decimal AmountToPay { get; set; }
        
        public string CallbackUrl { get; set; }
        
        public ClientInfo ClientInfo { get; set; }
    }

    public class ClientInfo
    {
        public string UserAgent { get; set; }
        public string IpAddress { get; set; }
        public string DeviceFingerprint { get; set; }
    }

    public class AnonymousPaymentVerifyRequest
    {
        [Required]
        public string MerchantTransactionId { get; set; }
        
        [Required]
        public string OrderId { get; set; }
        
        [Required]
        public string BookingId { get; set; }
    }

    // Response DTOs
    public class AnonymousBookingDetailsResponse
    {
        public string BookingId { get; set; }
        public string TripType { get; set; }
        public string PickUpCity { get; set; }
        public string DropOffCity { get; set; }
        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }
        public string PickUpDate { get; set; }
        public string PickUpTime { get; set; }
        public string CarCategory { get; set; }
        public string CarFeatures { get; set; }
        public int CarCapacity { get; set; }
        public decimal Distance { get; set; }
        public string Duration { get; set; }
        public decimal BasicFare { get; set; }
        public decimal Gst { get; set; }
        public decimal TotalFare { get; set; }
        public string PerKMCharges { get; set; }
        public string FixRateNote { get; set; }
        public decimal TollCharge { get; set; }
        public string TravelerName { get; set; }
        public string PhoneNumber { get; set; }
        public string MailId { get; set; }
        public string PaymentStatus { get; set; }
        public bool IsPaymentPending { get; set; }
        public decimal CashAmountToPayDriver { get; set; }
        public DateTime BookingCreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }

        // Partial Payment Details
        public string PaymentType { get; set; } // "PARTIAL" or "FULL"
        public decimal? PartialPaymentAmount { get; set; }
        public decimal? RemainingAmountForDriver { get; set; }
        public bool IsPartialPayment { get; set; }
        public decimal OnlinePaymentAmount { get; set; } // Amount to be paid online
        public decimal DriverPaymentAmount { get; set; } // Amount to be paid to driver
    }

    public class AnonymousPaymentInitiateResponse
    {
        public string TokenUrl { get; set; }
        public string MerchantTransactionId { get; set; }
        public string OrderId { get; set; }
        public string BookingId { get; set; }
        public DateTime ExpiresAt { get; set; }
        public decimal Amount { get; set; }
    }

    public class AnonymousPaymentVerifyResponse
    {
        public string PaymentStatus { get; set; }
        public string TransactionId { get; set; }
        public string OrderId { get; set; }
        public string BookingId { get; set; }
        public decimal Amount { get; set; }
        public string PaymentId { get; set; }
        public string PaymentType { get; set; }
        public DateTime PaymentDate { get; set; }
        public decimal? PartialPaymentAmount { get; set; }
        public decimal RemainingAmountForDriver { get; set; }
        public string ReceiptNumber { get; set; }
    }

    public class AnonymousPaymentReceiptResponse
    {
        public string BookingId { get; set; }
        public string ReceiptNumber { get; set; }
        public string TripType { get; set; }
        public string PickUpCity { get; set; }
        public string DropOffCity { get; set; }
        public string PickUpAddress { get; set; }
        public string DropOffAddress { get; set; }
        public string PickUpDate { get; set; }
        public string PickUpTime { get; set; }
        public string CarCategory { get; set; }
        public string CarFeatures { get; set; }
        public int CarCapacity { get; set; }
        public decimal Distance { get; set; }
        public string Duration { get; set; }
        public decimal BasicFare { get; set; }
        public decimal Gst { get; set; }
        public decimal TotalFare { get; set; }
        public string PaymentType { get; set; }
        public string PaymentStatus { get; set; }
        public decimal AmountPaid { get; set; }
        public decimal RemainingAmountForDriver { get; set; }
        public string TravelerName { get; set; }
        public string PhoneNumber { get; set; }
        public string MailId { get; set; }
        public string TransactionId { get; set; }
        public string PaymentId { get; set; }
        public DateTime PaymentDate { get; set; }
        public DateTime BookingDate { get; set; }
        public DriverDetails DriverDetails { get; set; }
        public CompanyDetails CompanyDetails { get; set; }

        // Partial Payment Details
        public string BookingPaymentType { get; set; } // "PARTIAL" or "FULL"
        public decimal? PartialPaymentAmount { get; set; }
        public bool IsPartialPayment { get; set; }
        public string PaymentInstructions { get; set; }
    }

    public class DriverDetails
    {
        public DateTime WillBeSharedAt { get; set; }
        public string Message { get; set; }
    }

    public class CompanyDetails
    {
        public string Name { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Website { get; set; }
    }

    public class CarCategoryDetails
    {
        public int PKID { get; set; }
        public string CategoryName { get; set; }
        public string Features { get; set; }
        public int Capacity { get; set; }
        public string CategoryImage { get; set; }
        public decimal PerKMCharges { get; set; }
        public decimal BaseFare { get; set; }
    }
}
