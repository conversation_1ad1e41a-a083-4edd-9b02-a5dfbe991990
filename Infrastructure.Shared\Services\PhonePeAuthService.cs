﻿using Application.DTOs.Payment;
using Application.Interfaces.Services;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using System.Collections.Generic;

namespace Infrastructure.Services
{
    public class PhonePeAuthService : IPhonePeAuthService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _cache;
        private const string TOKEN_CACHE_KEY = "PhonePeAccessToken";
        private const string TOKEN_EXPIRY_CACHE_KEY = "PhonePeTokenExpiry";

        public PhonePeAuthService(
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration,
            IMemoryCache cache)
        {
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _cache = cache;
        }

        public async Task<string> GetAccessTokenAsync()
        {
            try
            {
                Console.WriteLine("Starting PhonePe token generation");
                
                // Check if we have a valid token in cache
                if (_cache.TryGetValue(TOKEN_CACHE_KEY, out string cachedToken))
                {
                    Console.WriteLine("Found cached token, checking validity");
                    if (await IsTokenValidAsync())
                    {
                        Console.WriteLine("Using valid cached token");
                        return cachedToken;
                    }
                    Console.WriteLine("Cached token is invalid or expired");
                }

                // Get new token
                var client = _httpClientFactory.CreateClient("PhonePe");
                var phonePeSettings = _configuration.GetSection("PaymentSettings:PhonePe");

                Console.WriteLine($"Using PhonePe API URL: {client.BaseAddress}");

                // Prepare form data for token request - matching exact curl format
                var formData = new Dictionary<string, string>
                {
                    { "client_id", phonePeSettings["ApiKey"] },
                    { "client_version", "1" },
                    { "client_secret", phonePeSettings["ApiSecret"] },
                    { "grant_type", "client_credentials" }
                };

                // Log the request parameters (excluding sensitive data)
                Console.WriteLine("Token request parameters:");
                Console.WriteLine($"client_version: {formData["client_version"]}");
                Console.WriteLine($"grant_type: {formData["grant_type"]}");
                Console.WriteLine($"client_id: {phonePeSettings["ApiKey"]}");

                var content = new FormUrlEncodedContent(formData);
                Console.WriteLine("Sending token request to PhonePe");
                Console.WriteLine($"Request URL: {client.BaseAddress}/v1/oauth/token");

                // Use the exact endpoint from the curl example
                var oauthURL = String.IsNullOrWhiteSpace(phonePeSettings["oauthApiUrl"])
                    ? $"{client.BaseAddress}/v1/oauth/token"
                    : $"{phonePeSettings["oauthApiUrl"]}/v1/oauth/token";
                var response = await client.PostAsync(oauthURL, content);
                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"PhonePe token response: {responseContent}");

                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"PhonePe token request failed: {response.StatusCode} - {responseContent}");
                    throw new Exception($"PhonePe token request failed: {response.StatusCode} - {responseContent}");
                }

                var authResponse = JsonSerializer.Deserialize<PhonePeAuthResponse>(responseContent);
                Console.WriteLine("Successfully received token from PhonePe");

                // Cache the token
                var cacheOptions = new MemoryCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(authResponse.ExpiresIn ?? 3600)); // Default to 1 hour if not specified

                _cache.Set(TOKEN_CACHE_KEY, authResponse.AccessToken, cacheOptions);
                _cache.Set(TOKEN_EXPIRY_CACHE_KEY, DateTimeOffset.UtcNow.AddSeconds(authResponse.ExpiresIn ?? 3600).ToUnixTimeSeconds(), cacheOptions);

                Console.WriteLine("Token cached successfully");
                return authResponse.AccessToken;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting PhonePe token: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<bool> IsTokenValidAsync()
        {
            if (!_cache.TryGetValue(TOKEN_EXPIRY_CACHE_KEY, out long expiryTime))
            {
                Console.WriteLine("No expiry time found in cache");
                return false;
            }

            var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var isValid = currentTime < (expiryTime - 300); // Add 5-minute buffer before expiry
            Console.WriteLine($"Token validity check - Current: {currentTime}, Expiry: {expiryTime}, IsValid: {isValid}");
            return isValid;
        }
    }
}