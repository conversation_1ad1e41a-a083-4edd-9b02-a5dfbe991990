using Application.DTOs.Payment;
using Application.Exceptions;
using Application.Features.Payments.PhonePe;
using Application.Interfaces.Repositories;
using Application.Interfaces.Services;
using Application.Wrappers;
using MediatR;
using Microsoft.Extensions.Configuration;
using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Application.Utilities;

namespace Application.Features.Payments.AnonymousPayments
{
    public class VerifyAnonymousPaymentCommand : IRequest<Response<AnonymousPaymentVerifyResponse>>
    {
        public AnonymousPaymentVerifyRequest Request { get; set; }
    }

    public class VerifyAnonymousPaymentCommandHandler : IRequestHandler<VerifyAnonymousPaymentCommand, Response<AnonymousPaymentVerifyResponse>>
    {
        private readonly IBookingRepositoryAsync _bookingRepository;
        private readonly IPhonePeAuthService _phonePeAuthService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;

        public VerifyAnonymousPaymentCommandHandler(
            IBookingRepositoryAsync bookingRepository,
            IPhonePeAuthService phonePeAuthService,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration)
        {
            _bookingRepository = bookingRepository;
            _phonePeAuthService = phonePeAuthService;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
        }

        public async Task<Response<AnonymousPaymentVerifyResponse>> Handle(VerifyAnonymousPaymentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Decrypt the booking ID
                var encryptionPassphrase = _configuration["AnonymousPaymentSettings:EncryptionPassphrase"];
                var bookingId = QueryStringEncoding.DecryptFromUrl(request.Request.BookingId, encryptionPassphrase);

                Console.WriteLine($"[VerifyAnonymousPayment] Decrypted booking ID: {bookingId}");

                // Validate that this is an admin booking
                var isAdminBooking = await _bookingRepository.IsAdminBookingAsync(bookingId);
                if (!isAdminBooking)
                {
                    throw new ApiException("This booking is not available for anonymous payment.");
                }

                // Get PhonePe access token
                var accessToken = await _phonePeAuthService.GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    throw new ApiException("Failed to get PhonePe access token");
                }

                // Get PhonePe settings
                var phonePeSettings = _configuration.GetSection("PaymentSettings:PhonePe");
                var apiUrl = phonePeSettings["ApiUrl"];

                // Verify payment with PhonePe - using the same endpoint as VerifyPhonePePayment
                var client = _httpClientFactory.CreateClient("PhonePe");
                client.DefaultRequestHeaders.Add("Authorization", $"O-Bearer {accessToken}");

                // The MerchantTransactionId should now be the booking ID (same as working implementation)
                var statusUrl = $"{apiUrl}/checkout/v2/order/{request.Request.MerchantTransactionId}/status";
                Console.WriteLine($"[VerifyAnonymousPayment] Calling PhonePe status API: {statusUrl}");
                Console.WriteLine($"[VerifyAnonymousPayment] Using MerchantTransactionId (BookingId): {request.Request.MerchantTransactionId}");
                Console.WriteLine($"[VerifyAnonymousPayment] OrderId: {request.Request.OrderId}");

                var response = await client.GetAsync(statusUrl);
                Console.WriteLine($"[VerifyAnonymousPayment] PhonePe API Response Status: {response.StatusCode}");

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[VerifyAnonymousPayment] Error response from PhonePe API: {errorContent}");
                    throw new ApiException($"PhonePe payment verification failed: {errorContent}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"[VerifyAnonymousPayment] PhonePe Verify Response: {responseContent}");
                Console.WriteLine($"[VerifyAnonymousPayment] Response Length: {responseContent?.Length ?? 0}");

                // Check if response is empty or null
                if (string.IsNullOrWhiteSpace(responseContent))
                {
                    throw new ApiException("PhonePe returned empty response. Payment may still be processing.");
                }

                // Check if response looks like JSON
                if (!responseContent.TrimStart().StartsWith("{") && !responseContent.TrimStart().StartsWith("["))
                {
                    throw new ApiException($"PhonePe returned non-JSON response: {responseContent}");
                }

                PhonePeOrderStatusResponse phonePeOrderResponse;
                try
                {
                    phonePeOrderResponse = JsonSerializer.Deserialize<PhonePeOrderStatusResponse>(responseContent);
                }
                catch (JsonException ex)
                {
                    Console.WriteLine($"[VerifyAnonymousPayment] JSON Deserialization Error: {ex.Message}");
                    throw new ApiException($"Failed to parse PhonePe response: {ex.Message}. Response: {responseContent}");
                }

                // Check if response is valid
                if (phonePeOrderResponse == null)
                {
                    throw new ApiException("PhonePe returned null response");
                }

                // Map PhonePe status to application payment status
                var phonePeStatus = phonePeOrderResponse.state;
                Console.WriteLine($"[VerifyAnonymousPayment] Payment Status from PhonePe: {phonePeStatus}");
                Console.WriteLine($"[VerifyAnonymousPayment] PhonePe OrderId: {phonePeOrderResponse.orderId}");
                Console.WriteLine($"[VerifyAnonymousPayment] PhonePe Amount: {phonePeOrderResponse.amount}");

                // Handle different payment statuses
                switch (phonePeStatus?.ToUpper())
                {
                    case PhonePePaymentStatus.SUCCESS:
                    case PhonePePaymentStatus.COMPLETED:
                        Console.WriteLine($"[VerifyAnonymousPayment] Payment successful");
                        break;
                    case PhonePePaymentStatus.PENDING:
                        throw new ApiException("Payment is still pending. Please try again in a few moments.");
                    case PhonePePaymentStatus.FAILED:
                        throw new ApiException("Payment failed. Please try again or contact support.");
                    default:
                        throw new ApiException($"Unknown payment status: {phonePeStatus}");
                }

                // Extract payment details from PhonePe response
                var transactionId = "";
                var paymentId = "";

                if (phonePeOrderResponse.paymentDetails != null && phonePeOrderResponse.paymentDetails.Count > 0)
                {
                    var paymentDetail = phonePeOrderResponse.paymentDetails[0];
                    transactionId = paymentDetail.transactionId;
                    paymentId = paymentDetail.transactionId; // Use transactionId as paymentId
                }

                Console.WriteLine($"[VerifyAnonymousPayment] Extracted TransactionId: {transactionId}, PaymentId: {paymentId}");

                // Process the payment in our system
                var paymentResult = await _bookingRepository.ProcessAnonymousPaymentAsync(
                    request.Request.MerchantTransactionId,
                    request.Request.OrderId,
                    bookingId,
                    paymentId,
                    "PhonePe");

                if (paymentResult == null)
                {
                    throw new ApiException("Failed to process payment in system");
                }

                // Get booking details to determine payment type
                var booking = await _bookingRepository.GetAnonymousBookingByIdAsync(bookingId);
                var isPartialPayment = booking != null && !string.IsNullOrEmpty(booking.PaymentType) && booking.PaymentType.Equals("PARTIAL", StringComparison.OrdinalIgnoreCase);

                Console.WriteLine($"[VerifyAnonymousPayment] Payment Type: {booking?.PaymentType}");
                Console.WriteLine($"[VerifyAnonymousPayment] Is Partial Payment: {isPartialPayment}");
                Console.WriteLine($"[VerifyAnonymousPayment] Partial Payment Amount: {booking?.PartialPaymentAmount}");
                Console.WriteLine($"[VerifyAnonymousPayment] Remaining Amount for Driver: {booking?.RemainingAmountForDriver}");

                // Generate receipt number
                var receiptNumber = await _bookingRepository.GenerateReceiptNumberAsync(bookingId);

                var result = new AnonymousPaymentVerifyResponse
                {
                    PaymentStatus = "COMPLETED",
                    TransactionId = request.Request.MerchantTransactionId,
                    OrderId = request.Request.OrderId,
                    BookingId = request.Request.BookingId, // Return encrypted ID
                    Amount = phonePeOrderResponse.amount / 100, // Convert from paise to rupees
                    PaymentId = paymentId,
                    PaymentType = "PhonePe",
                    PaymentDate = DateTime.UtcNow,
                    PartialPaymentAmount = isPartialPayment ? booking?.PartialPaymentAmount : null,
                    RemainingAmountForDriver = isPartialPayment ? (booking?.RemainingAmountForDriver ?? 0) : 0,
                    ReceiptNumber = receiptNumber
                };

                return new Response<AnonymousPaymentVerifyResponse>(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[VerifyAnonymousPayment] Error: {ex.Message}");
                throw new ApiException($"Failed to verify payment: {ex.Message}");
            }
        }
    }
}
