<?xml version="1.0"?>
<doc>
    <assembly>
        <name>WebApi</name>
    </assembly>
    <members>
        <member name="M:WebApi.Controllers.v1.AnonymousPaymentController.GetAnonymousBookingDetails(System.String)">
            <summary>
            Get anonymous booking details for admin-created bookings
            </summary>
            <param name="bookingId">Encrypted booking ID</param>
            <returns>Booking details for anonymous payment</returns>
        </member>
        <member name="M:WebApi.Controllers.v1.AnonymousPaymentController.InitiateAnonymousPayment(Application.DTOs.Payment.AnonymousPaymentInitiateRequest)">
            <summary>
            Initiate anonymous payment for admin-created booking
            </summary>
            <param name="request">Payment initiation request</param>
            <returns>Payment token URL and transaction details</returns>
        </member>
        <member name="M:WebApi.Controllers.v1.AnonymousPaymentController.VerifyAnonymousPayment(Application.DTOs.Payment.AnonymousPaymentVerifyRequest)">
            <summary>
            Verify anonymous payment completion
            </summary>
            <param name="request">Payment verification request</param>
            <returns>Payment verification result</returns>
        </member>
        <member name="M:WebApi.Controllers.v1.AnonymousPaymentController.GetAnonymousPaymentReceipt(System.String)">
            <summary>
            Get anonymous payment receipt
            </summary>
            <param name="bookingId">Encrypted booking ID</param>
            <returns>Payment receipt details</returns>
        </member>
    </members>
</doc>
