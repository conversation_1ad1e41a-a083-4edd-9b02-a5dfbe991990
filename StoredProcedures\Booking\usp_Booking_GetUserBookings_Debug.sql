-- =============================================
-- Debug Version: usp_Booking_GetUserBookings_Debug
-- This version will help us understand what's happening with user matching
-- =============================================

USE [CabYaari]
GO

-- Drop existing procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'usp_Booking_GetUserBookings_Debug')
BEGIN
    DROP PROCEDURE [dbo].[usp_Booking_GetUserBookings_Debug]
END
GO

CREATE PROCEDURE [dbo].[usp_Booking_GetUserBookings_Debug]
    @UserId NVARCHAR(450),
    @Cursor NVARCHAR(50) = NULL,
    @PageSize INT = 10
AS
BEGIN
    SET NOCOUNT ON;

    PRINT '=== DEBUG: USER BOOKINGS ANALYSIS ===';
    PRINT 'Input UserId: ' + ISNULL(@UserId, 'NULL');
    
    -- Check if user exists in Identity.User table
    DECLARE @UserExists INT = 0;
    DECLARE @UserIdFromTable NVARCHAR(450);
    DECLARE @UserEmail NVARCHAR(256);
    
    SELECT 
        @UserExists = COUNT(*),
        @UserIdFromTable = MAX(Id),
        @UserEmail = MAX(Email)
    FROM [Identity].[User] 
    WHERE UserName = @UserId OR Id = @UserId;
    
    PRINT 'User exists in Identity.User: ' + CASE WHEN @UserExists > 0 THEN 'YES' ELSE 'NO' END;
    IF @UserExists > 0
    BEGIN
        PRINT 'User Id from table: ' + ISNULL(@UserIdFromTable, 'NULL');
        PRINT 'User Email: ' + ISNULL(@UserEmail, 'NULL');
    END
    
    -- Check bookings by different matching methods
    DECLARE @BookingsByCreatedBy INT = 0;
    DECLARE @BookingsByUserJoin INT = 0;
    DECLARE @BookingsByEmail INT = 0;
    
    -- Method 1: Direct Booking_Created_By match
    SELECT @BookingsByCreatedBy = COUNT(*) 
    FROM [dbo].[RLT_BOOKING] 
    WHERE Booking_Created_By = @UserId;
    
    -- Method 2: Created_By + User table join
    SELECT @BookingsByUserJoin = COUNT(*) 
    FROM [dbo].[RLT_BOOKING] b 
    INNER JOIN [Identity].[User] u ON CAST(b.Created_By AS NVARCHAR) = u.Id 
    WHERE u.UserName = @UserId;
    
    -- Method 3: Email match (if user exists)
    IF @UserExists > 0
    BEGIN
        SELECT @BookingsByEmail = COUNT(*) 
        FROM [dbo].[RLT_BOOKING] 
        WHERE Mail_Id = @UserEmail;
    END
    
    PRINT 'Bookings found by Booking_Created_By: ' + CAST(@BookingsByCreatedBy AS NVARCHAR(10));
    PRINT 'Bookings found by Created_By + User join: ' + CAST(@BookingsByUserJoin AS NVARCHAR(10));
    PRINT 'Bookings found by Email match: ' + CAST(@BookingsByEmail AS NVARCHAR(10));
    
    -- Show sample booking data
    PRINT '';
    PRINT '=== SAMPLE BOOKING DATA ===';
    
    SELECT TOP 3
        Booking_Id,
        Created_By,
        Booking_Created_By,
        [Name],
        Mail_Id,
        Created_Date
    FROM [dbo].[RLT_BOOKING]
    ORDER BY PKID DESC;
    
    -- Now try to get actual results using the most promising method
    PRINT '';
    PRINT '=== ATTEMPTING TO RETRIEVE BOOKINGS ===';
    
    IF @BookingsByCreatedBy > 0
    BEGIN
        PRINT 'Using Booking_Created_By method...';
        SELECT TOP (@PageSize + 1)
            b.Booking_Id as BookingId,
            cf.CitY_Name as PickUpCity,
            ct.CitY_Name as DropOffCity,
            tt.Trip_Type as TripType,
            cc.Car_Category_Abbr as CarCategory,
            ROUND(b.Fare, 0) as Fare,
            b.PickUp_Address as PickUpAddress,
            b.DropOff_Address as DropOffAddress,
            b.PickUp_Date as PickUpDate,
            b.PickUp_Time as PickUpTime,
            b.[Name] as TravelerName,
            b.Mobile_No1 as PhoneNumber,
            b.razorpay_status as RazorpayStatus,
            b.Created_Date as BookingDate
        FROM [dbo].[RLT_BOOKING] b
        INNER JOIN [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
        INNER JOIN [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
        INNER JOIN [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
        INNER JOIN [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
        WHERE b.Booking_Created_By = @UserId
        ORDER BY b.Created_Date DESC, b.Booking_Id DESC;
    END
    ELSE IF @BookingsByUserJoin > 0
    BEGIN
        PRINT 'Using Created_By + User join method...';
        SELECT TOP (@PageSize + 1)
            b.Booking_Id as BookingId,
            cf.CitY_Name as PickUpCity,
            ct.CitY_Name as DropOffCity,
            tt.Trip_Type as TripType,
            cc.Car_Category_Abbr as CarCategory,
            ROUND(b.Fare, 0) as Fare,
            b.PickUp_Address as PickUpAddress,
            b.DropOff_Address as DropOffAddress,
            b.PickUp_Date as PickUpDate,
            b.PickUp_Time as PickUpTime,
            b.[Name] as TravelerName,
            b.Mobile_No1 as PhoneNumber,
            b.razorpay_status as RazorpayStatus,
            b.Created_Date as BookingDate
        FROM [dbo].[RLT_BOOKING] b
        INNER JOIN [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
        INNER JOIN [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
        INNER JOIN [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
        INNER JOIN [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
        INNER JOIN [Identity].[User] u ON CAST(b.Created_By AS NVARCHAR) = u.Id
        WHERE u.UserName = @UserId
        ORDER BY b.Created_Date DESC, b.Booking_Id DESC;
    END
    ELSE IF @BookingsByEmail > 0
    BEGIN
        PRINT 'Using Email match method...';
        SELECT TOP (@PageSize + 1)
            b.Booking_Id as BookingId,
            cf.CitY_Name as PickUpCity,
            ct.CitY_Name as DropOffCity,
            tt.Trip_Type as TripType,
            cc.Car_Category_Abbr as CarCategory,
            ROUND(b.Fare, 0) as Fare,
            b.PickUp_Address as PickUpAddress,
            b.DropOff_Address as DropOffAddress,
            b.PickUp_Date as PickUpDate,
            b.PickUp_Time as PickUpTime,
            b.[Name] as TravelerName,
            b.Mobile_No1 as PhoneNumber,
            b.razorpay_status as RazorpayStatus,
            b.Created_Date as BookingDate
        FROM [dbo].[RLT_BOOKING] b
        INNER JOIN [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
        INNER JOIN [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
        INNER JOIN [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
        INNER JOIN [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
        WHERE b.Mail_Id = @UserEmail
        ORDER BY b.Created_Date DESC, b.Booking_Id DESC;
    END
    ELSE
    BEGIN
        PRINT 'No bookings found with any method!';
        PRINT 'This suggests either:';
        PRINT '1. The UserId parameter is incorrect';
        PRINT '2. The user has no bookings';
        PRINT '3. There is a data consistency issue';
    END
END
GO

PRINT 'usp_Booking_GetUserBookings_Debug created successfully!'
PRINT 'Usage: EXEC [dbo].[usp_Booking_GetUserBookings_Debug] @UserId = ''your_user_id'''
