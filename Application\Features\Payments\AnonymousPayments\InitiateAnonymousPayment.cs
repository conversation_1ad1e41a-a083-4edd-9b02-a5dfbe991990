using Application.DTOs.Payment;
using Application.Enums;
using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Interfaces.Services;
using Application.Wrappers;
using MediatR;
using Microsoft.Extensions.Configuration;
using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Application.Utilities;

namespace Application.Features.Payments.AnonymousPayments
{
    public class InitiateAnonymousPaymentCommand : IRequest<Response<AnonymousPaymentInitiateResponse>>
    {
        public AnonymousPaymentInitiateRequest Request { get; set; }
    }

    public class InitiateAnonymousPaymentCommandHandler : IRequestHandler<InitiateAnonymousPaymentCommand, Response<AnonymousPaymentInitiateResponse>>
    {
        private readonly IBookingRepositoryAsync _bookingRepository;
        private readonly IPhonePeAuthService _phonePeAuthService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;

        public InitiateAnonymousPaymentCommandHandler(
            IBookingRepositoryAsync bookingRepository,
            IPhonePeAuthService phonePeAuthService,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration)
        {
            _bookingRepository = bookingRepository;
            _phonePeAuthService = phonePeAuthService;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
        }

        public async Task<Response<AnonymousPaymentInitiateResponse>> Handle(InitiateAnonymousPaymentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Decrypt the booking ID
                var encryptionPassphrase = _configuration["AnonymousPaymentSettings:EncryptionPassphrase"];
                var bookingId = QueryStringEncoding.DecryptFromUrl(request.Request.BookingId, encryptionPassphrase);

                Console.WriteLine($"[InitiateAnonymousPayment] Decrypted booking ID: {bookingId}");

                // Validate that this is an admin booking
                var isAdminBooking = await _bookingRepository.IsAdminBookingAsync(bookingId);
                if (!isAdminBooking)
                {
                    throw new ApiException("This booking is not available for anonymous payment.");
                }

                // Get booking details
                var booking = await _bookingRepository.GetAnonymousBookingByIdAsync(bookingId);
                if (booking == null)
                {
                    throw new ApiException("Booking not found.");
                }

                // Determine expected payment amount based on payment type
                var isPartialPayment = !string.IsNullOrEmpty(booking.PaymentType) && booking.PaymentType.Equals("PARTIAL", StringComparison.OrdinalIgnoreCase);
                var expectedAmount = isPartialPayment ? (booking.PartialPaymentAmount ?? 0) : (booking.Fare ?? 0);

                Console.WriteLine($"[InitiateAnonymousPayment] Payment Type: {booking.PaymentType}");
                Console.WriteLine($"[InitiateAnonymousPayment] Is Partial Payment: {isPartialPayment}");
                Console.WriteLine($"[InitiateAnonymousPayment] Expected Amount: {expectedAmount}");
                Console.WriteLine($"[InitiateAnonymousPayment] Requested Amount: {request.Request.AmountToPay}");
                Console.WriteLine($"[InitiateAnonymousPayment] Requested PaymentOption: {request.Request.PaymentOption}");

                // Validate PaymentOption matches the booking's PaymentType
                var expectedPaymentOption = isPartialPayment ? (int)PaymentOption.PartialPay : (int)PaymentOption.FullPay;
                if (request.Request.PaymentOption != expectedPaymentOption)
                {
                    var paymentTypeText = isPartialPayment
                        ? $"partial payment (PaymentOption={PaymentOption.PartialPay})"
                        : $"full payment (PaymentOption={PaymentOption.FullPay})";
                    throw new ApiException($"PaymentOption mismatch. This booking requires {paymentTypeText}. Provided: {request.Request.PaymentOption}");
                }

                // Validate payment amount
                if (request.Request.AmountToPay != expectedAmount)
                {
                    var paymentTypeText = isPartialPayment ? "partial payment" : "full payment";
                    throw new ApiException($"Payment amount mismatch for {paymentTypeText}. Expected: {expectedAmount}, Provided: {request.Request.AmountToPay}");
                }

                // Generate unique transaction ID for tracking
                var merchantTransactionId = $"ANON_TXN_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}";

                // Use the actual booking ID as MerchantOrderId (same as working implementation)
                var merchantOrderId = bookingId; // This should be the actual booking ID like CY-200725-85918

                Console.WriteLine($"[InitiateAnonymousPayment] Using BookingId as MerchantOrderId: {merchantOrderId}");
                Console.WriteLine($"[InitiateAnonymousPayment] Generated MerchantTransactionId: {merchantTransactionId}");

                // Get PhonePe access token
                var accessToken = await _phonePeAuthService.GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    throw new ApiException("Failed to get PhonePe access token");
                }

                // Convert amount to paise
                var amountInPaise = (long)(request.Request.AmountToPay * 100);

                // Get PhonePe settings
                var phonePeSettings = _configuration.GetSection("PaymentSettings:PhonePe");
                var apiUrl = phonePeSettings["ApiUrl"];
                var callbackUrl = string.IsNullOrWhiteSpace(request.Request.CallbackUrl)
                    ? _configuration["AnonymousPaymentSettings:CallbackUrl"]
                    : request.Request.CallbackUrl;

                // Prepare payment request - using booking ID as MerchantOrderId (same as working implementation)
                var paymentRequest = new PhonePePaymentInitiateRequest
                {
                    MerchantOrderId = merchantOrderId, // Use actual booking ID
                    Amount = amountInPaise,
                    PaymentFlow = new PhonePePaymentFlow
                    {
                        Type = "PG_CHECKOUT",
                        Message = $"Payment for booking {bookingId}",
                        MerchantUrls = new PhonePeMerchantUrls
                        {
                            RedirectUrl = callbackUrl
                        }
                    },
                    MetaInfo = new PhonePeMetaInfo
                    {
                        Udf1 = booking.TravelerName,
                        Udf2 = booking.PhoneNumber,
                        Udf3 = booking.PickUpAddress,
                        Udf4 = booking.DropOffAddress,
                        Udf5 = booking.CarCategory
                    }
                };

                // Make API call to initiate payment - using the same endpoint as GeneratePhonePeTokenCommand
                var client = _httpClientFactory.CreateClient("PhonePe");
                client.DefaultRequestHeaders.Add("Authorization", $"O-Bearer {accessToken}");

                var content = new StringContent(
                    JsonSerializer.Serialize(paymentRequest),
                    Encoding.UTF8,
                    "application/json");

                Console.WriteLine($"[InitiateAnonymousPayment] Sending payment initiation request to PhonePe");
                Console.WriteLine($"[InitiateAnonymousPayment] Payment request details: {JsonSerializer.Serialize(paymentRequest)}");

                var response = await client.PostAsync($"{apiUrl}/checkout/v2/pay", content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"[InitiateAnonymousPayment] PhonePe API error: {response.StatusCode} - {errorContent}");
                    throw new ApiException($"PhonePe payment initiation failed: {errorContent}");
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"[InitiateAnonymousPayment] PhonePe Response: {responseContent}");

                var phonePeResponse = JsonSerializer.Deserialize<PhonePePaymentInitiateResponse>(responseContent);

                var result = new AnonymousPaymentInitiateResponse
                {
                    TokenUrl = phonePeResponse.RedirectUrl,
                    MerchantTransactionId = merchantOrderId, // Return the booking ID (used for verification)
                    OrderId = phonePeResponse.OrderId, // Return PhonePe's order ID
                    BookingId = request.Request.BookingId, // Return encrypted ID
                    ExpiresAt = DateTimeOffset.FromUnixTimeMilliseconds(phonePeResponse.ExpireAt).DateTime, // Convert from milliseconds
                    Amount = request.Request.AmountToPay
                };

                Console.WriteLine($"[InitiateAnonymousPayment] Returning MerchantTransactionId: {result.MerchantTransactionId}");
                Console.WriteLine($"[InitiateAnonymousPayment] Returning PhonePe OrderId: {result.OrderId}");

                return new Response<AnonymousPaymentInitiateResponse>(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[InitiateAnonymousPayment] Error: {ex.Message}");
                throw new ApiException($"Failed to initiate payment: {ex.Message}");
            }
        }
    }
}
