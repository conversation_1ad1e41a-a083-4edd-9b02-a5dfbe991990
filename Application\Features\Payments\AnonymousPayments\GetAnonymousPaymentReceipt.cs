using Application.DTOs.Payment;
using Application.Exceptions;
using Application.Interfaces.Repositories;
using Application.Wrappers;
using MediatR;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading;
using System.Threading.Tasks;
using Application.Utilities;

namespace Application.Features.Payments.AnonymousPayments
{
    public class GetAnonymousPaymentReceiptQuery : IRequest<Response<AnonymousPaymentReceiptResponse>>
    {
        public string EncryptedBookingId { get; set; }
    }

    public class GetAnonymousPaymentReceiptQueryHandler : IRequestHandler<GetAnonymousPaymentReceiptQuery, Response<AnonymousPaymentReceiptResponse>>
    {
        private readonly IBookingRepositoryAsync _bookingRepository;
        private readonly IConfiguration _configuration;

        public GetAnonymousPaymentReceiptQueryHandler(
            IBookingRepositoryAsync bookingRepository,
            IConfiguration configuration)
        {
            _bookingRepository = bookingRepository;
            _configuration = configuration;
        }

        public async Task<Response<AnonymousPaymentReceiptResponse>> Handle(GetAnonymousPaymentReceiptQuery request, CancellationToken cancellationToken)
        {
            try
            {
                // Decrypt the booking ID
                var encryptionPassphrase = _configuration["AnonymousPaymentSettings:EncryptionPassphrase"];
                var bookingId = QueryStringEncoding.DecryptFromUrl(request.EncryptedBookingId, encryptionPassphrase);

                Console.WriteLine($"[GetAnonymousPaymentReceipt] Decrypted booking ID: {bookingId}");

                // Validate that this is an admin booking
                var isAdminBooking = await _bookingRepository.IsAdminBookingAsync(bookingId);
                if (!isAdminBooking)
                {
                    throw new ApiException("This booking is not available for anonymous payment.");
                }

                // Get booking details
                var booking = await _bookingRepository.GetAnonymousBookingByIdAsync(bookingId);
                if (booking == null)
                {
                    throw new ApiException("Booking not found.");
                }

                // Check if payment is completed
                if (booking.RazorpayStatus != "Paid")
                {
                    throw new ApiException("Payment not completed for this booking.");
                }

                // Get car category details from database
                var carCategoryDetails = await _bookingRepository.GetCarCategoryDetailsAsync(booking.CarCategory);

                Console.WriteLine($"[GetAnonymousPaymentReceipt] Car Category: {booking.CarCategory}");
                Console.WriteLine($"[GetAnonymousPaymentReceipt] Car Details Found: {carCategoryDetails != null}");
                if (carCategoryDetails != null)
                {
                    Console.WriteLine($"[GetAnonymousPaymentReceipt] Capacity: {carCategoryDetails.Capacity}, Features: {carCategoryDetails.Features}");
                }

                // Generate receipt number
                var receiptNumber = await _bookingRepository.GenerateReceiptNumberAsync(bookingId);

                // Calculate driver details sharing time (30 minutes before pickup)
                var pickupDateTime = booking.PickUpDate?.Date.Add(TimeSpan.Parse(booking.PickUpTime ?? "00:00")) ?? DateTime.UtcNow;
                var driverDetailsShareTime = pickupDateTime.AddMinutes(-30);

                // Determine payment type and amounts
                var isPartialPayment = !string.IsNullOrEmpty(booking.PaymentType) && booking.PaymentType.Equals("PARTIAL", StringComparison.OrdinalIgnoreCase);
                var amountPaid = isPartialPayment ? (booking.PartialPaymentAmount ?? 0) : (booking.Fare ?? 0);
                var paymentInstructions = isPartialPayment
                    ? $"You have paid ₹{amountPaid} online. Please pay the remaining ₹{booking.RemainingAmountForDriver ?? 0} to the driver."
                    : $"Full payment of ₹{amountPaid} completed online.";

                Console.WriteLine($"[GetAnonymousPaymentReceipt] Payment Type: {booking.PaymentType}");
                Console.WriteLine($"[GetAnonymousPaymentReceipt] Is Partial Payment: {isPartialPayment}");
                Console.WriteLine($"[GetAnonymousPaymentReceipt] Amount Paid: {amountPaid}");
                Console.WriteLine($"[GetAnonymousPaymentReceipt] Remaining for Driver: {booking.RemainingAmountForDriver}");

                var response = new AnonymousPaymentReceiptResponse
                {
                    BookingId = request.EncryptedBookingId, // Return encrypted ID
                    ReceiptNumber = receiptNumber,
                    TripType = booking.TripType,
                    PickUpCity = booking.PickUpCity,
                    DropOffCity = booking.DropOffCity,
                    PickUpAddress = booking.PickUpAddress,
                    DropOffAddress = booking.DropOffAddress,
                    PickUpDate = booking.PickUpDate?.ToString("yyyy-MM-dd"),
                    PickUpTime = booking.PickUpTime,
                    CarCategory = booking.CarCategory,
                    CarFeatures = carCategoryDetails?.Features ?? "AC, Music System, GPS Tracking", // Use DB features or fallback
                    CarCapacity = carCategoryDetails?.Capacity ?? 4, // Use DB capacity or fallback
                    Distance = booking.Distance ?? 0, // Now stores actual distance directly
                    Duration = booking.Duration,
                    BasicFare = booking.BasicFare ?? 0,
                    Gst = booking.Gst ?? 0,
                    TotalFare = booking.Fare ?? 0,
                    PaymentType = "PhonePe UPI",
                    PaymentStatus = "COMPLETED",
                    AmountPaid = amountPaid,
                    RemainingAmountForDriver = booking.RemainingAmountForDriver ?? 0,
                    TravelerName = booking.TravelerName,
                    PhoneNumber = booking.PhoneNumber,
                    MailId = booking.MailId,
                    TransactionId = booking.RazorpayPaymentId ?? "N/A",
                    PaymentId = booking.RazorpayOrderid ?? "N/A",
                    PaymentDate = booking.BookingDate ?? DateTime.UtcNow,
                    BookingDate = booking.BookingDate ?? DateTime.UtcNow,

                    // Partial Payment Details
                    BookingPaymentType = booking.PaymentType ?? "FULL",
                    PartialPaymentAmount = booking.PartialPaymentAmount,
                    IsPartialPayment = isPartialPayment,
                    PaymentInstructions = paymentInstructions,
                    DriverDetails = new DriverDetails
                    {
                        WillBeSharedAt = driverDetailsShareTime,
                        Message = "Driver details will be shared 30 minutes before pickup"
                    },
                    CompanyDetails = new CompanyDetails
                    {
                        Name = "CabYaari",
                        Address = "Your Company Address",
                        Phone = "+91-XXXXXXXXXX",
                        Email = "<EMAIL>",
                        Website = "https://cabyaari.com"
                    }
                };

                return new Response<AnonymousPaymentReceiptResponse>(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[GetAnonymousPaymentReceipt] Error: {ex.Message}");
                throw new ApiException($"Failed to retrieve receipt: {ex.Message}");
            }
        }
    }
}
