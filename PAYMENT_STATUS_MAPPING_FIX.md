# Payment Status Mapping Fix

## Issue Identified
There was a mismatch between the payment status values used in the C# verification code and the stored procedure `usp_Booking_Update_V2`.

## Problem Details

### Before Fix:
- **C# Code**: Was mapping PhonePe "SUCCESS" to `"Success"`
- **Stored Procedure**: Was checking for `'COMPLETED'`, `'SUCCESS'`, `'Paid'`
- **PaymentStatus Enum**: Uses `"Pending"`, `"Paid"`, `"Failed"`, etc.

This caused a mismatch where the C# code would set status as `"Success"` but the stored procedure was looking for `'Paid'`.

## Changes Made

### 1. Updated C# Verification Code
**File:** `Application/Features/Payments/PhonePe/VerifyPhonePePayment.cs`

**Before:**
```csharp
switch (phonePeStatus?.ToUpper())
{
    case "SUCCESS":
        paymentStatus = "Success";  // ❌ Wrong value
        break;
    case "FAILED":
        paymentStatus = "Failed";
        break;
    case "PENDING":
        paymentStatus = "Pending";
        break;
    default:
        paymentStatus = "Pending";
        break;
}

// Extract transaction details if payment is completed
if (paymentStatus == "COMPLETED" && ...) // ❌ Wrong check
```

**After:**
```csharp
switch (phonePeStatus?.ToUpper())
{
    case "SUCCESS":
        paymentStatus = "Paid";  // ✅ Matches PaymentStatus enum
        break;
    case "FAILED":
        paymentStatus = "Failed";
        break;
    case "PENDING":
        paymentStatus = "Pending";
        break;
    default:
        paymentStatus = "Pending";
        break;
}

// Extract transaction details if payment is completed
if (paymentStatus == "Paid" && ...) // ✅ Correct check
```

### 2. Updated Stored Procedures
**Files:** `STORED_PROCEDURES_V2.sql` and `DATABASE_SCHEMA_CHANGES.sql`

**Before:**
```sql
SET @BookingStatusId = CASE 
    WHEN @RazorpayStatus IN ('COMPLETED', 'SUCCESS', 'Paid') THEN 2 
    WHEN @RazorpayStatus IN ('FAILED', 'CANCELLED') THEN 3
    ELSE 1 
END
```

**After:**
```sql
-- Normalize razorpay_status: convert numeric values to text
-- Handle legacy numeric status values: 1 = Pending, 2 = Paid, 3 = Failed
SET @RazorpayStatus = CASE
    WHEN @RazorpayStatus = '1' THEN 'Pending'
    WHEN @RazorpayStatus = '2' THEN 'Paid'
    WHEN @RazorpayStatus = '3' THEN 'Failed'
    ELSE @RazorpayStatus  -- Keep original text values
END

SET @BookingStatusId = CASE
    WHEN @RazorpayStatus IN ('Paid', 'COMPLETED') THEN 2  -- Successful booking (PKID 2)
    WHEN @RazorpayStatus IN ('Failed') THEN 1  -- Failed booking (PKID 1)
    ELSE 3  -- Payment Pending (PKID 3)
END
```

## Status Mapping Reference

### PaymentStatus Enum Values:
- `Pending = 1` - Payment is pending
- `Paid = 2` - Payment completed successfully
- `Failed = 3` - Payment failed
- `Created = 4` - Payment order created
- `PartialPaid = 5` - Partial payment completed
- `CashPaidToDriver = 6` - Cash paid to driver
- `Attempted = 7` - Payment attempted

### RLT_BOOKING_STATUS Table Values (Database):
- `PKID = 1` - "Failed" - Payment failed/booking cancelled
- `PKID = 2` - "Successful" - Payment successful/booking confirmed
- `PKID = 3` - "Payment Pending" - Payment is pending

### BookingStatus Enum Values (Code - NOT USED):
- `Pending = 1` - New booking request
- `Confirmed = 2` - Booking confirmed (payment successful)
- `CabAssigned = 3` - Cab assigned to booking
- `TripStated = 4` - Trip started
- `NotConfirmd = 5` - Booking not confirmed
- `Cancelled = 6` - Booking cancelled (payment failed)
- `Completed = 7` - Trip completed
- `TripAborted = 8` - Trip aborted

### PhonePe Status to Application Status Mapping:
| PhonePe Status | Application PaymentStatus | BookingStatus ID | Description |
|----------------|---------------------------|------------------|-------------|
| SUCCESS        | Paid                      | 2                | Payment successful, booking confirmed |
| FAILED         | Failed                    | 6                | Payment failed, booking cancelled |
| PENDING        | Pending                   | 1                | Payment pending, booking pending |

## Impact of Fix

### ✅ Benefits:
1. **Consistent Status Values**: All components now use the same status values
2. **Proper Booking Status Updates**: Bookings will be correctly marked as confirmed/cancelled
3. **Accurate Payment Tracking**: Payment status will be properly tracked in the database
4. **Better Error Handling**: Failed payments will be properly handled

### 🔧 What This Fixes:
- Payment verification will now correctly identify successful payments
- Booking status will be properly updated based on payment status
- Transaction details will be extracted for successful payments
- Database queries filtering by payment status will work correctly

## Testing Recommendations

1. **Test Successful Payment Flow**:
   - Verify that successful PhonePe payments set status to "Paid"
   - Confirm booking status is updated to 2 (Confirmed)
   - Check that transaction details are extracted

2. **Test Failed Payment Flow**:
   - Verify that failed PhonePe payments set status to "Failed"
   - Confirm booking status is updated to 1 (Failed)

3. **Test Pending Payment Flow**:
   - Verify that pending PhonePe payments set status to "Pending"
   - Confirm booking status is set to 3 (Payment Pending)

## Files Updated
- `Application/Features/Payments/PhonePe/VerifyPhonePePayment.cs`
- `STORED_PROCEDURES_V2.sql`
- `DATABASE_SCHEMA_CHANGES.sql`
- `UPDATE_STORED_PROCEDURE_MODE_OF_PAYMENT.sql`
- `DATABASE_SCHEMA_PHONEPE_PAYMENT_METHOD.sql`

## Additional Fixes
- **Numeric Status Handling**: Updated stored procedures to handle legacy numeric `razorpay_status` values (1, 2, 3) and convert them to appropriate text values
- **Data Cleanup Script**: Created `CLEANUP_RAZORPAY_STATUS_NUMERIC_VALUES.sql` to clean up existing numeric values in the database

### Numeric to Text Conversion:
- `razorpay_status = '1'` → `'Pending'` (Booking_Status_Id = 3)
- `razorpay_status = '2'` → `'Paid'` (Booking_Status_Id = 2)
- `razorpay_status = '3'` → `'Failed'` (Booking_Status_Id = 1)

This fix ensures that the payment status mapping is consistent throughout the entire payment processing pipeline and handles both legacy numeric values and modern text values.
