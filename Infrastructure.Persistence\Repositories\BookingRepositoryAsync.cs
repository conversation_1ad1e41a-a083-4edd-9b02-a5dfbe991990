﻿using Application.DTOs.Booking;
using Application.DTOs.Payment;
using Application.Enums;
using Application.Features.Bookings.Common;
using Application.Interfaces.Repositories;
using Dapper;
using Domain.Entities;
using Domain.Settings;
using Infrastructure.Persistence.Contexts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Razorpay.Api;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Infrastructure.Persistence.Repositories
{
    public class BookingRepositoryAsync : IBookingRepositoryAsync
    {

        private readonly IConfiguration configuration;
        private readonly ApplicationDbContext _dbContext;
        private readonly PaymentSettings paymentSettings;
        public BookingRepositoryAsync(IConfiguration configuration, ApplicationDbContext dbContext, IOptions<PaymentSettings> paymentSettings)
        {

            this.configuration = configuration;

            _dbContext = dbContext;
            this.paymentSettings = paymentSettings.Value;
        }

        public async Task<string> AddAsync(RLT_BOOKING entity)
        {
            string bookingId = string.Empty;
            var bookingID = BookingUtility.GenerateBookingID();

            var orderId = RazorPayPaymentOrderCreate(Convert.ToDecimal(entity.Fare), bookingID);

            if (orderId == null)
            {
                return null;
            }

            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                DynamicParameters _params = new DynamicParameters();
                _params.Add("@Booking_Id", bookingID, DbType.String);
                _params.Add("@PickUpCity", entity.PickUpCity, DbType.String);
                _params.Add("@DropOffCity", entity.DropOffCity, DbType.String);
                _params.Add("@TripType", entity.TripType, DbType.String);
                _params.Add("@CarCategory", entity.CarCategory, DbType.String);
                _params.Add("@Duration", entity.Duration, DbType.String);
                _params.Add("@Distance", entity.Distance, DbType.Decimal);
                _params.Add("@BasicFare", entity.BasicFare, DbType.Decimal);
                _params.Add("@DriverCharge", entity.DriverCharge, DbType.Decimal);
                _params.Add("@Gst", entity.Gst, DbType.Decimal);
                _params.Add("@Fare", entity.Fare, DbType.Decimal);
                _params.Add("@GstFare", entity.GstFare, DbType.Decimal);
                _params.Add("@CouponCode", entity.CouponCode, DbType.String);
                _params.Add("@CouponDiscount", entity.CouponDiscount, DbType.Decimal);
                _params.Add("@PickUpAddress", entity.PickUpAddress, DbType.String);
                _params.Add("@DropOffAddress", entity.DropOffAddress, DbType.String);
                _params.Add("@PickUpDate", entity.PickUpDate, DbType.Date);
                _params.Add("@PickUpTime", entity.PickUpTime, DbType.String);
                _params.Add("@TravelerName", entity.TravelerName, DbType.String);
                _params.Add("@PhoneNumber", entity.PhoneNumber, DbType.String);
                _params.Add("@MailId", entity.MailId, DbType.String);
                _params.Add("@PaymentMode", entity.PaymentMode, DbType.Int32);
                _params.Add("@BookingCreatedBy", entity.BookingCreatedBy, DbType.String);
                _params.Add("@RazorpayPaymentId", entity.RazorpayPaymentId, DbType.String);
                _params.Add("@RazorpayOrderid", orderId, DbType.String);
                _params.Add("@RazorpaySignature", entity.RazorpaySignature, DbType.String);
                _params.Add("@RazorpayStatus", PaymentStatus.Pending, DbType.String);
                _params.Add("@PickUpAddressLongLat", entity.PickUpAddressLongLat, DbType.String);
                _params.Add("@PickUpAddressLongitude", entity.DropOffAddressLongLat, DbType.String);
                _params.Add("@CashAmountToPayDriver", entity.CashAmountToPayDriver, DbType.Decimal);
                _params.Add("@PaymentOption", entity.PaymentOption, DbType.Int32);
                _params.Add("@TollCharge", entity.TollCharge, DbType.Decimal);
                _params.Add("@result", DbType.String, direction: ParameterDirection.Output);
                var result = await connection.ExecuteAsync("usp_Booking_Create", _params, commandType: CommandType.StoredProcedure).ConfigureAwait(false);
                if (result != null)
                {
                    int Id = _params.Get<Int32>("result");
                    if (Id != null)
                    {
                        bookingId = bookingID;
                    }
                }

                return bookingId;
            }

        }

        public async Task DeleteAsync(RLT_BOOKING entity)
        {
            throw new NotImplementedException();
        }

        public async Task<IReadOnlyList<RLT_BOOKING>> GetAllAsync()
        {
            throw new NotImplementedException();
        }

        public async Task<RLT_BOOKING> GetByUniqueIdAsync(string id)
        {
            var _params = new
            {
                bookingID = id
            };
            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();
                var result = await connection.QueryFirstOrDefaultAsync<RLT_BOOKING>("usp_BookingDetails_Get", _params, commandType: CommandType.StoredProcedure);
                return result;
            }
        }

        public Task<IReadOnlyList<RLT_BOOKING>> GetPagedReponseAsync(int pageNumber, int pageSize)
        {
            throw new NotImplementedException();
        }

        public async Task UpdateAsync(RLT_BOOKING entity)
        {
            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                DynamicParameters _params = new DynamicParameters();
                _params.Add("@BookingId", entity.BookingID, DbType.String);
                _params.Add("@RazorpayPaymentId", entity.RazorpayPaymentId, DbType.String);
                _params.Add("@RazorpayOrderid", entity.RazorpayOrderid, DbType.String);
                _params.Add("@RazorpaySignature", entity.RazorpaySignature ?? "", DbType.String);
                _params.Add("@RazorpayStatus", entity.RazorpayStatus, DbType.String);
                _params.Add("@PaymentType", entity.PaymentType, DbType.String);
                _params.Add("@PartialPaymentAmount", entity.PartialPaymentAmount, DbType.Decimal);
                _params.Add("@RemainingAmountForDriver", entity.RemainingAmountForDriver, DbType.Decimal);
                _params.Add("@PhonePePaymentMethod", entity.PhonePePaymentMethod, DbType.String);

                var result = await connection.ExecuteAsync("usp_Booking_Update_V2", _params, commandType: CommandType.StoredProcedure).ConfigureAwait(false);
            }
        }


        public async Task<RazorPaymentResponse> RazorPayPaymentSuccess(string paymentId, string razorpayOrderId, string razorpaySignature)
        {
            RazorPaymentResponse razorPaymentResponse = new RazorPaymentResponse();

            string payment_Id = paymentId;
            string razorpay_order_id = razorpayOrderId;

            int paymentStatus = (int)PaymentStatus.Pending;


            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {


                if (paymentId != null)
                {
                    string key = this.paymentSettings.PaymentKey;
                    string secret = this.paymentSettings.PaymentSecret;

                    RazorpayClient client = new RazorpayClient(key, secret);
                    //var paymetStatus = client.Payment.Fetch(razorpayOrderId);

                    Dictionary<string, string> attributes = new Dictionary<string, string>();

                    attributes.Add("razorpay_payment_id", paymentId);
                    attributes.Add("razorpay_order_id", razorpay_order_id);
                    attributes.Add("razorpay_signature", razorpaySignature);
                    Utils.verifyPaymentSignature(attributes);
                }
                connection.Open();

                DynamicParameters _params = new DynamicParameters();
                _params.Add("@RazorpayPaymentId", paymentId, DbType.String);
                _params.Add("@RazorpayOrderid", razorpay_order_id, DbType.String);
                _params.Add("@RazorpaySignature", razorpaySignature, DbType.String);
                _params.Add("@RazorpayStatus", PaymentStatus.Paid, DbType.String);
                var result = await connection.QueryFirstOrDefaultAsync<RazorPaymentResponse>("usp_Booking_update", _params, commandType: CommandType.StoredProcedure).ConfigureAwait(false);

                if (result.BookingId != null && result.TransactionId != null)
                {
                    razorPaymentResponse.BookingId = result.BookingId;
                    razorPaymentResponse.TransactionId = result.TransactionId;
                    razorPaymentResponse.PaymentStatus = result.PaymentStatus == Convert.ToString(PaymentStatus.Paid) ? "Paid" : "";
                }

                return result;


            }

        }
        private string RazorPayPaymentOrderCreate(decimal fare, string bookingId)
        {

            int Amount = Convert.ToInt32(fare) * 100;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            Dictionary<string, object> input = new Dictionary<string, object>();
            input.Add("amount", Amount); // this amount should be same as transaction amount
            input.Add("currency", "INR");
            input.Add("receipt", bookingId);
            input.Add("payment_capture", 1);
            string key = this.paymentSettings.PaymentKey;
            string secret = this.paymentSettings.PaymentSecret;
            RazorpayClient client = new RazorpayClient(key, secret);
            Razorpay.Api.Order order = client.Order.Create(input);
            string orderId = order["id"].ToString();

            return orderId;
        }

        public async Task<BookingResponse> AddNewAsync(RLT_BOOKING entity)
        {
            BookingResponse bookingResponse = new BookingResponse();

            string bookingId = string.Empty;
            var bookingID = BookingUtility.GenerateBookingID();

            var orderId = RazorPayPaymentOrderCreate(Convert.ToDecimal(entity.Fare), bookingID);

            if (orderId == null)
            {
                return null;
            }

            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                DynamicParameters _params = new DynamicParameters();
                _params.Add("@Booking_Id", bookingID, DbType.String);
                _params.Add("@PickUpCity", entity.PickUpCity, DbType.String);
                _params.Add("@DropOffCity", entity.DropOffCity, DbType.String);
                _params.Add("@TripType", entity.TripType, DbType.String);
                _params.Add("@CarCategory", entity.CarCategory, DbType.String);
                _params.Add("@Duration", entity.Duration, DbType.String);
                _params.Add("@Distance", entity.Distance, DbType.Decimal);
                _params.Add("@BasicFare", entity.BasicFare, DbType.Decimal);
                _params.Add("@DriverCharge", entity.DriverCharge, DbType.Decimal);
                _params.Add("@Gst", entity.Gst, DbType.Decimal);
                _params.Add("@Fare", entity.Fare, DbType.Decimal);
                _params.Add("@GstFare", entity.GstFare, DbType.Decimal);
                _params.Add("@CouponCode", entity.CouponCode, DbType.String);
                _params.Add("@CouponDiscount", entity.CouponDiscount, DbType.Decimal);
                _params.Add("@PickUpAddress", entity.PickUpAddress, DbType.String);
                _params.Add("@DropOffAddress", entity.DropOffAddress, DbType.String);
                _params.Add("@PickUpDate", entity.PickUpDate, DbType.Date);
                _params.Add("@PickUpTime", entity.PickUpTime, DbType.String);
                _params.Add("@TravelerName", entity.TravelerName, DbType.String);
                _params.Add("@PhoneNumber", entity.PhoneNumber, DbType.String);
                _params.Add("@MailId", entity.MailId, DbType.String);
                _params.Add("@PaymentMode", entity.PaymentMode, DbType.Int32);
                _params.Add("@BookingCreatedBy", entity.BookingCreatedBy, DbType.String);
                _params.Add("@RazorpayPaymentId", entity.RazorpayPaymentId, DbType.String);
                _params.Add("@RazorpayOrderid", orderId, DbType.String);
                _params.Add("@RazorpaySignature", entity.RazorpaySignature, DbType.String);
                _params.Add("@RazorpayStatus", PaymentStatus.Pending, DbType.String);
                _params.Add("@PickUpAddressLongLat", entity.PickUpAddressLongLat, DbType.String);
                _params.Add("@PickUpAddressLongitude", entity.DropOffAddressLongLat, DbType.String);
                _params.Add("@CashAmountToPayDriver", entity.CashAmountToPayDriver, DbType.Decimal);
                _params.Add("@PaymentOption", entity.PaymentOption, DbType.Int32);
                _params.Add("@TollCharge", entity.TollCharge, DbType.Decimal);
                _params.Add("@PaymentType", entity.PaymentType, DbType.String);
                _params.Add("@PartialPaymentAmount", entity.PartialPaymentAmount, DbType.Decimal);
                _params.Add("@RemainingAmountForDriver", entity.RemainingAmountForDriver, DbType.Decimal);
                _params.Add("@result", DbType.String, direction: ParameterDirection.Output);
                var result = await connection.ExecuteAsync("usp_Booking_Create_V2", _params, commandType: CommandType.StoredProcedure).ConfigureAwait(false);
                if (result != null)
                {
                    int Id = _params.Get<Int32>("result");
                    if (Id != null)
                    {
                        bookingResponse.BookingId = bookingID;
                        bookingResponse.OrderId = orderId;
                    }
                }

                return bookingResponse;
            }
        }

        public async Task<UserBookingsResponse> GetUserBookingsByUserIdAsync(string userId, string cursor, int pageSize)
        {
            Console.WriteLine($"[DEBUG] GetUserBookingsByUserIdAsync called with UserId: {userId}, Cursor: {cursor}, PageSize: {pageSize}");

            var response = new UserBookingsResponse();

            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                // Use stored procedure for better performance and maintainability
                DynamicParameters parameters = new DynamicParameters();
                parameters.Add("@UserId", userId, DbType.String);
                parameters.Add("@Cursor", cursor, DbType.String);
                parameters.Add("@PageSize", pageSize, DbType.Int32);

                Console.WriteLine($"[DEBUG] Calling stored procedure with parameters - UserId: {userId}, Cursor: {cursor}, PageSize: {pageSize}");

                var bookings = await connection.QueryAsync<UserBookingItem>(
                    "usp_Booking_GetUserBookings_V4",
                    parameters,
                    commandType: CommandType.StoredProcedure);
                var bookingList = bookings.ToList();

                Console.WriteLine($"[DEBUG] Retrieved {bookingList.Count} bookings from database");

                // Log details of each booking for debugging
                for (int i = 0; i < bookingList.Count; i++)
                {
                    var booking = bookingList[i];
                    Console.WriteLine($"[DEBUG] Booking {i + 1}: BookingId={booking.BookingId}, BookingDate={booking.BookingDate}, PickUpDate={booking.PickUpDate}");
                }

                // Check if there are more results
                response.HasMoreResult = bookingList.Count > pageSize;
                Console.WriteLine($"[DEBUG] HasMoreResult: {response.HasMoreResult} (bookingList.Count: {bookingList.Count}, pageSize: {pageSize})");

                // Remove the extra item if we have more results
                if (response.HasMoreResult)
                {
                    Console.WriteLine($"[DEBUG] Removing extra item from results");
                    bookingList.RemoveAt(bookingList.Count - 1);
                }

                response.Bookings = bookingList;
                response.TotalCount = bookingList.Count;

                // Set next cursor - use BookingId as cursor since BookingDate is null
                // BookingId format is CY-DDMMYY-NNNNN, so we can use it for pagination
                if (response.HasMoreResult && bookingList.Any())
                {
                    var lastBooking = bookingList.Last();
                    Console.WriteLine($"[DEBUG] Last booking for cursor: BookingId={lastBooking.BookingId}, BookingDate={lastBooking.BookingDate}");

                    // If BookingDate is available, use it; otherwise use BookingId
                    if (lastBooking.BookingDate.HasValue)
                    {
                        response.NextCursor = lastBooking.BookingDate.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        Console.WriteLine($"[DEBUG] Using BookingDate as cursor: {response.NextCursor}");
                    }
                    else
                    {
                        // Use BookingId as fallback cursor
                        response.NextCursor = lastBooking.BookingId;
                        Console.WriteLine($"[DEBUG] Using BookingId as cursor: {response.NextCursor}");
                    }
                }
                else
                {
                    Console.WriteLine($"[DEBUG] No cursor set - HasMoreResult: {response.HasMoreResult}, BookingList.Any(): {bookingList.Any()}");
                }

                Console.WriteLine($"[DEBUG] Final response - TotalCount: {response.TotalCount}, HasMoreResult: {response.HasMoreResult}, NextCursor: {response.NextCursor}");

                return response;
            }
        }

        // Anonymous Payment Methods for Admin Bookings
        public async Task<RLT_BOOKING> GetAnonymousBookingByIdAsync(string bookingId)
        {
            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                // First try to get by PKID if the bookingId is numeric
                if (int.TryParse(bookingId, out int pkid))
                {
                    var query = @"SELECT * FROM RLT_BOOKING WHERE PKID = @PKID";
                    var result = await connection.QueryFirstOrDefaultAsync<RLT_BOOKING>(query, new { PKID = pkid });
                    if (result != null) return result;
                }

                // If not found by PKID or not numeric, try by Booking_Id using stored procedure
                var _params = new { bookingID = bookingId };
                var spResult = await connection.QueryFirstOrDefaultAsync<RLT_BOOKING>("usp_BookingDetails_Get", _params, commandType: CommandType.StoredProcedure);
                return spResult;
            }
        }

        public async Task<bool> IsAdminBookingAsync(string bookingId)
        {
            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();
                // Check both PKID and Booking_Id to handle both cases
                var query = @"SELECT CASE WHEN IsAdminBooked = 1 THEN 1 ELSE 0 END
                             FROM RLT_BOOKING
                             WHERE Booking_Id = @BookingId OR CAST(PKID AS NVARCHAR) = @BookingId";
                var result = await connection.QueryFirstOrDefaultAsync<bool>(query, new { BookingId = bookingId });
                return result;
            }
        }

        public async Task<AnonymousPaymentVerifyResponse> ProcessAnonymousPaymentAsync(string merchantTransactionId, string orderId, string bookingId, string paymentId, string paymentType)
        {
            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                // Get the actual Booking_Id if we received PKID
                string actualBookingId = bookingId;
                if (int.TryParse(bookingId, out int pkid))
                {
                    var bookingIdQuery = "SELECT Booking_Id FROM RLT_BOOKING WHERE PKID = @PKID";
                    actualBookingId = await connection.QueryFirstOrDefaultAsync<string>(bookingIdQuery, new { PKID = pkid });
                    if (string.IsNullOrEmpty(actualBookingId))
                    {
                        actualBookingId = bookingId; // Fallback to original
                    }
                }

                DynamicParameters _params = new DynamicParameters();
                _params.Add("@BookingId", actualBookingId, DbType.String);
                _params.Add("@MerchantTransactionId", merchantTransactionId, DbType.String);
                _params.Add("@OrderId", orderId, DbType.String);
                _params.Add("@PaymentId", paymentId, DbType.String);
                _params.Add("@PaymentType", paymentType, DbType.String);
                _params.Add("@PaymentStatus", "COMPLETED", DbType.String);
                _params.Add("@PaymentDate", DateTime.UtcNow, DbType.DateTime);

                var result = await connection.QueryFirstOrDefaultAsync<AnonymousPaymentVerifyResponse>(
                    "usp_AnonymousPayment_Process",
                    _params,
                    commandType: CommandType.StoredProcedure);

                return result;
            }
        }

        public async Task<string> GenerateReceiptNumberAsync(string bookingId)
        {
            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();

                // Handle both PKID and Booking_Id
                var query = @"SELECT 'RCPT_ANON_' + RIGHT('000000' + CAST(PKID AS VARCHAR(6)), 6)
                             FROM RLT_BOOKING
                             WHERE Booking_Id = @BookingId OR CAST(PKID AS NVARCHAR) = @BookingId";
                var result = await connection.QueryFirstOrDefaultAsync<string>(query, new { BookingId = bookingId });
                return result;
            }
        }

        public async Task<CarCategoryDetails> GetCarCategoryDetailsAsync(string categoryName)
        {
            using (var connection = new SqlConnection(configuration.GetConnectionString("DefaultConnection")))
            {
                connection.Open();
                var query = @"SELECT
                                PKID,
                                Car_Category_Abbr as CategoryName,
                                Features,
                                Capacity,
                                Car_Categroy_Image as CategoryImage,
                                Per_KM_fare as PerKMCharges,
                                Base_Fare as BaseFare
                             FROM RLT_CAR_CATEGORY
                             WHERE Is_Active = 1 AND Car_Category_Abbr = @CategoryName";

                var result = await connection.QueryFirstOrDefaultAsync<CarCategoryDetails>(query, new { CategoryName = categoryName });
                return result;
            }
        }
    }
}
