-- =============================================
-- Deploy Updated Stored Procedures
-- Description: Updates existing stored procedures with booking status fixes
-- Date: 2025-01-08
-- =============================================

USE [CabYaari]
GO

PRINT '=== DEPLOYING UPDATED STORED PROCEDURES ==='
PRINT 'Updating stored procedures with:'
PRINT '1. Correct booking status mapping (1=Failed, 2=Successful, 3=Payment Pending)'
PRINT '2. Numeric razorpay_status conversion (1→Pending, 2→Paid, 3→Failed)'
PRINT ''
GO

-- =============================================
-- Update: usp_Booking_Update_V2
-- Description: Enhanced version with booking status fixes
-- =============================================
ALTER PROCEDURE [dbo].[usp_Booking_Update_V2]
    @BookingId nvarchar(30),
    @RazorpayPaymentId nvarchar(30),
    @RazorpayOrderid nvarchar(30),
    @RazorpaySignature nvarchar(30),
    @RazorpayStatus nvarchar(30),
    -- New parameters for partial payment support
    @PaymentType nvarchar(10) NULL,
    @PartialPaymentAmount decimal(18,2) NULL,
    @RemainingAmountForDriver decimal(18,2) NULL,
    -- New parameter for PhonePe payment method string
    @PhonePePaymentMethod nvarchar(50) NULL
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    DECLARE @errorMessage nvarchar(max),
           @ScriptRanOn datetime = GETDATE(),
           @TransactionId nvarchar(30),
           @BookingStatusId int,
           @BookingPKID int,
           @CurrentPaymentType nvarchar(10),
           @CurrentPartialAmount decimal(18,2),
           @CurrentRemainingAmount decimal(18,2),
           @ModeOfPaymentId int,
           @CorrectPaymentOption nvarchar(50)

    -- Map PhonePe payment method to Mode_Of_Payment_Id
    IF @PhonePePaymentMethod IS NOT NULL
    BEGIN
        SET @ModeOfPaymentId = CASE @PhonePePaymentMethod
            WHEN 'UPI' THEN 3
            WHEN 'CARD' THEN 1
            WHEN 'NET_BANKING' THEN 4
            WHEN 'WALLET' THEN 5
            ELSE 2  -- Default to PhonePe (PKID 2)
        END
        
        -- Set correct PaymentOption based on PhonePe payment method
        SET @CorrectPaymentOption = CASE @PhonePePaymentMethod
            WHEN 'UPI' THEN 'UPI'
            WHEN 'CARD' THEN 'Card'
            WHEN 'NET_BANKING' THEN 'Net Banking'
            WHEN 'WALLET' THEN 'Wallet'
            ELSE 'PhonePe'
        END
        
        PRINT 'PhonePe Payment Method: ' + @PhonePePaymentMethod + ', Mapped to Mode_Of_Payment_Id: ' + CAST(@ModeOfPaymentId AS nvarchar(10))
    END

    -- Get current payment information
    SELECT
        @CurrentPaymentType = PaymentType,
        @CurrentPartialAmount = PartialPaymentAmount,
        @CurrentRemainingAmount = RemainingAmountForDriver
    FROM RLT_BOOKING
    WHERE Booking_Id = @BookingId

    -- Normalize razorpay_status: convert numeric values to text
    -- Handle legacy numeric status values: 1 = Pending, 2 = Paid, 3 = Failed
    SET @RazorpayStatus = CASE
        WHEN @RazorpayStatus = '1' THEN 'Pending'
        WHEN @RazorpayStatus = '2' THEN 'Paid'  
        WHEN @RazorpayStatus = '3' THEN 'Failed'
        ELSE @RazorpayStatus  -- Keep original text values
    END

    -- Get the appropriate booking status ID based on payment status
    -- Updated to match RLT_BOOKING_STATUS table: 1=Failed, 2=Successful, 3=Payment Pending
    SET @BookingStatusId = CASE
        WHEN @RazorpayStatus IN ('Paid', 'COMPLETED') THEN 2  -- Successful booking (PKID 2)
        WHEN @RazorpayStatus IN ('Failed') THEN 1  -- Failed booking (PKID 1)
        ELSE 3  -- Payment Pending (PKID 3)
    END

    SET @errorMessage = CONVERT(nvarchar(max), GETDATE(), 21) + ' running usp_Booking_Update_V2 => starting sql transaction'
    RAISERROR(@errorMessage, 0, 1) WITH NOWAIT

    BEGIN TRANSACTION
        -- Update the booking record with payment information
        UPDATE RLT_BOOKING
        SET
            razorpay_payment_id = @RazorpayPaymentId,
            razorpay_signature = @RazorpaySignature,
            razorpay_status = @RazorpayStatus,
            Booking_Status_Id = @BookingStatusId,
            -- Update payment fields only if new values are provided
            PaymentType = ISNULL(@PaymentType, @CurrentPaymentType),
            PartialPaymentAmount = ISNULL(@PartialPaymentAmount, @CurrentPartialAmount),
            RemainingAmountForDriver = ISNULL(@RemainingAmountForDriver, @CurrentRemainingAmount),
            -- Update PaymentOption to match PaymentType
            PaymentOption = ISNULL(@CorrectPaymentOption, PaymentOption),
            -- Update Mode_Of_Payment_Id based on PhonePe payment method lookup
            Mode_Of_Payment_Id = ISNULL(@ModeOfPaymentId, Mode_Of_Payment_Id),
            Updated_Date = GETDATE()
        WHERE
            Booking_Id = @BookingId

        -- Get the booking information for the response
        SELECT
            @TransactionId = razorpay_payment_id,
            @BookingPKID = PKID
        FROM
            RLT_BOOKING
        WHERE
            Booking_Id = @BookingId

        SET @errorMessage = CONVERT(nvarchar(max), GETDATE(), 21) + ' running usp_Booking_Update_V2 => committing sql transaction'
        RAISERROR(@errorMessage, 0, 1) WITH NOWAIT
    COMMIT TRANSACTION

    -- Return the booking and transaction information
    SELECT
        @BookingId AS BookingId,
        @TransactionId AS TransactionId,
        @RazorpayStatus AS PaymentStatus,
        @PaymentType AS PaymentType,
        @PartialPaymentAmount AS PartialPaymentAmount,
        @RemainingAmountForDriver AS RemainingAmountForDriver
END
GO

GO

PRINT 'usp_Booking_Update_V2 updated successfully!'
PRINT ''
GO

-- =============================================
-- Update: usp_BookingDetails_Get
-- Description: Fixed version with proper table joins
-- =============================================
ALTER PROCEDURE [dbo].[usp_BookingDetails_Get]
    @bookingID nvarchar(30)
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    SELECT
        b.PKID,
        b.Booking_Id AS BookingID,
        cf.CitY_Name AS PickUpCity,
        ct.CitY_Name AS DropOffCity,
        tt.Trip_Type AS TripType,
        cc.Car_Category_Abbr AS CarCategory,
        b.Duration,
        b.Distance,
        b.Basic_Fare AS BasicFare,
        b.Driver_Charge AS DriverCharge,
        b.GST AS Gst,
        b.Fare,
        b.GST_Fare AS GstFare,
        b.Coupon_Code AS CouponCode,
        b.Coupon_Discount AS CouponDiscount,
        b.Booking_Date AS BookingDate,
        b.PickUp_Address AS PickUpAddress,
        b.DropOff_Address AS DropOffAddress,
        b.PickUp_Date AS PickUpDate,
        b.PickUp_Time AS PickUpTime,
        b.[Name] AS TravelerName,
        b.Mobile_No1 AS PhoneNumber,
        b.Mail_Id AS MailId,
        b.Mode_Of_Payment_Id AS PaymentMode,
        b.Booking_Status_Id AS BookingStatusId,
        -- Try multiple approaches to get the correct user identifier
        CASE
            WHEN b.Booking_Created_By IS NOT NULL AND b.Booking_Created_By != '' THEN b.Booking_Created_By
            WHEN b.Created_By IS NOT NULL THEN CAST(b.Created_By AS NVARCHAR(50))
            ELSE b.Mail_Id  -- Fallback to email
        END AS BookingCreatedBy,
        b.razorpay_payment_id AS RazorpayPaymentId,
        b.razorpay_order_id AS RazorpayOrderid,
        b.razorpay_signature AS RazorpaySignature,
        b.razorpay_status AS RazorpayStatus,
        b.PickUpAddressLatitude AS PickUpAddressLongLat,
        b.PickUpAddressLongitude AS DropOffAddressLongLat,
        b.CashAmountToPayDriver,
        b.PaymentOption,
        b.TollCharge,
        b.PaymentType,
        b.PartialPaymentAmount,
        b.RemainingAmountForDriver
    FROM
        [dbo].[RLT_BOOKING] b
    INNER JOIN
        [dbo].[RLT_CITY] cf ON b.City_From_Id = cf.PKID
    INNER JOIN
        [dbo].[RLT_CITY] ct ON b.City_To_Id = ct.PKID
    INNER JOIN
        [dbo].[RLT_TRIP_TYPES] tt ON b.Trip_Type_Id = tt.PKID
    INNER JOIN
        [dbo].[RLT_CAR_CATEGORY] cc ON b.Car_Category_Id = cc.PKID
    WHERE
        b.Booking_Id = @bookingID
END
GO

GO

PRINT 'usp_BookingDetails_Get updated successfully!'
PRINT ''

PRINT '=== DEPLOYMENT COMPLETED ==='
PRINT 'Updated stored procedures:'
PRINT '- usp_Booking_Update_V2: Now handles numeric razorpay_status conversion and correct booking status mapping'
PRINT '- usp_BookingDetails_Get: Fixed with proper table joins for city, trip type, and car category lookups'
PRINT ''
PRINT 'Status Mappings:'
PRINT '- razorpay_status "1" → "Pending" (Booking_Status_Id = 3)'
PRINT '- razorpay_status "2" → "Paid" (Booking_Status_Id = 2)'
PRINT '- razorpay_status "3" → "Failed" (Booking_Status_Id = 1)'
PRINT ''
PRINT 'Booking Status Mappings:'
PRINT '- Booking_Status_Id = 1 → "Failed" (RLT_BOOKING_STATUS PKID 1)'
PRINT '- Booking_Status_Id = 2 → "Successful" (RLT_BOOKING_STATUS PKID 2)'
PRINT '- Booking_Status_Id = 3 → "Payment Pending" (RLT_BOOKING_STATUS PKID 3)'
PRINT ''
PRINT 'Next steps:'
PRINT '1. Test payment processing with new bookings'
PRINT '2. Optionally run CLEANUP_RAZORPAY_STATUS_NUMERIC_VALUES.sql to fix existing data'
PRINT '3. Monitor booking status assignments'
