﻿using Application.DTOs.Booking;
using Application.Features.Bookings.CreateBooking;
using Application.Features.Products.Commands.CreateProduct;
using Application.Features.Products.Queries.GetAllProducts;
using Application.Enums;
using AutoMapper;
using Domain.Entities;
using System;
using System.Collections.Generic;
using System.Text;

namespace Application.Mappings
{
    public class GeneralProfile : Profile
    {
        public GeneralProfile()
        {
            CreateMap<Product, GetAllProductsViewModel>().ReverseMap();
            CreateMap<CreateProductCommand, Product>();
            CreateMap<GetAllProductsQuery, GetAllProductsParameter>();

            // ✅ Updated BookingCommand to RLT_BOOKING mapping with PaymentOption logic
            CreateMap<BookingCommand, RLT_BOOKING>()
                .ForMember(dest => dest.PaymentOption, opt => opt.MapFrom(src => MapPaymentTypeToPaymentOption(src.PaymentType)));

            // ✅ Add secure booking mapping (excludes sensitive data)
            CreateMap<RLT_BOOKING, BookingDetailsResponse>()
                .ForMember(dest => dest.PaymentStatus, opt => opt.Ignore()); // Handled in query handler

        }

        /// <summary>
        /// Maps PaymentType string to PaymentOption enum value
        /// PaymentOption = 1 for partial payments, PaymentOption = 2 for full payments
        /// </summary>
        private static int MapPaymentTypeToPaymentOption(string paymentType)
        {
            return paymentType?.ToUpper() switch
            {
                "PARTIAL" => (int)PaymentOption.PartialPay,  // 1 = Partial payment (online + cash to driver)
                "FULL" => (int)PaymentOption.FullPay,        // 2 = Full online payment
                _ => (int)PaymentOption.FullPay               // Default to full payment (2)
            };
        }
    }
}
