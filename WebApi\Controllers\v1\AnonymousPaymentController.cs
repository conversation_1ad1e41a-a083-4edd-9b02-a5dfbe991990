using Application.DTOs.Payment;
using Application.Features.Payments.AnonymousPayments;
using Application.Wrappers;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace WebApi.Controllers.v1
{
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/anonymous-payments")]
    public class AnonymousPaymentController : BaseApiController
    {
        /// <summary>
        /// Get anonymous booking details for admin-created bookings
        /// </summary>
        /// <param name="bookingId">Encrypted booking ID</param>
        /// <returns>Booking details for anonymous payment</returns>
        [HttpGet("booking/{bookingId}")]
        public async Task<IActionResult> GetAnonymousBookingDetails([Required] string bookingId)
        {
            var query = new GetAnonymousBookingDetailsQuery
            {
                EncryptedBookingId = bookingId
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Initiate anonymous payment for admin-created booking
        /// </summary>
        /// <param name="request">Payment initiation request</param>
        /// <returns>Payment token URL and transaction details</returns>
        [HttpPost("initiate")]
        public async Task<IActionResult> InitiateAnonymousPayment([FromBody] AnonymousPaymentInitiateRequest request)
        {
            var command = new InitiateAnonymousPaymentCommand
            {
                Request = request
            };

            var result = await Mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// Verify anonymous payment completion
        /// </summary>
        /// <param name="request">Payment verification request</param>
        /// <returns>Payment verification result</returns>
        [HttpPost("verify")]
        public async Task<IActionResult> VerifyAnonymousPayment([FromBody] AnonymousPaymentVerifyRequest request)
        {
            var command = new VerifyAnonymousPaymentCommand
            {
                Request = request
            };

            var result = await Mediator.Send(command);
            return Ok(result);
        }

        /// <summary>
        /// Get anonymous payment receipt
        /// </summary>
        /// <param name="bookingId">Encrypted booking ID</param>
        /// <returns>Payment receipt details</returns>
        [HttpGet("receipt/{bookingId}")]
        public async Task<IActionResult> GetAnonymousPaymentReceipt([Required] string bookingId)
        {
            var query = new GetAnonymousPaymentReceiptQuery
            {
                EncryptedBookingId = bookingId
            };

            var result = await Mediator.Send(query);
            return Ok(result);
        }
    }
}
